# 🤖 AutoPilot Options Trader

An AI-powered options trading platform designed for total beginners. One button: "Scan". The AI finds the best setups, chooses between Calls or Puts, and executes the trade with stop-loss & take-profit pre-set.

## ✨ Features

- **One-Click Trading**: Just click "Scan & Trade" and let the AI do the work
- **TTM Squeeze Strategy**: Advanced technical analysis to find high-probability setups
- **Automatic Risk Management**: Built-in stop-loss and take-profit levels
- **Paper Trading**: Safe testing environment before going live
- **Real-time P&L Tracking**: Monitor your trades with live updates
- **Comprehensive Logging**: All trades logged to CSV for analysis

## 🚀 Quick Start

### Prerequisites

1. **Python 3.8+** installed on your system
2. **Alpaca Trading Account** (free paper trading account)
3. **API Keys** from Alpaca

### Installation

1. **Clone or download** this repository to your computer

2. **Install dependencies**:
   ```bash
   pip install -r requirements.txt
   ```

3. **Set up your API keys**:
   - Create a `.env` file in the project directory
   - Add your Alpaca API credentials:
   ```
   ALPACA_API_KEY=your_api_key_here
   ALPACA_SECRET_KEY=your_secret_key_here
   ```

4. **Run the application**:

   **Option A - GUI Launcher (Recommended):**
   ```bash
   python launch_gui.py
   ```

   **Option B - Direct GUI Demo:**
   ```bash
   python gui_demo.py
   ```

   **Option C - Full Application:**
   ```bash
   python main.py
   ```

   **Option D - Windows Users:**
   ```bash
   start.bat
   ```

## 🎯 How It Works

### The AI Trading Process

1. **Market Scanning**: Scans S&P 500 stocks for trading opportunities
2. **Signal Detection**: Uses TTM Squeeze + Momentum analysis to find setups
3. **Option Selection**: Automatically selects the best option contracts
4. **Trade Execution**: Places trades with pre-calculated risk management
5. **Position Monitoring**: Tracks trades and executes exits automatically

### TTM Squeeze Strategy

The system looks for:
- **Squeeze Conditions**: When Bollinger Bands contract inside Keltner Channels
- **Momentum Breakouts**: Direction of the breakout (bullish/bearish)
- **Volume Confirmation**: Above-average volume supporting the move
- **RSI Levels**: Avoiding overbought/oversold extremes

## 🛡️ Risk Management

### Built-in Protections

- **Position Sizing**: Automatic calculation based on account size
- **Stop Loss**: Default 50% of premium paid
- **Take Profit**: Default 100% gain (double your money)
- **Daily Limits**: Maximum 3 trades per day
- **Liquidity Filters**: Only trades options with sufficient volume

### Safety Features

- **Paper Trading Default**: Starts in simulation mode
- **Minimum Requirements**: Filters for liquid, high-quality options
- **Account Protection**: Never risks more than 2% per trade

## 📊 GUI Overview

### Main Interface

- **Scan & Trade Button**: One-click market scanning and trading
- **Statistics Panel**: Today's trades, P&L, and win rate
- **Signals Table**: Current market opportunities
- **Positions Table**: Open trades and their status
- **Activity Log**: Real-time updates and trade notifications

### Settings Panel

- **Risk Parameters**: Adjust position sizing and stop-loss levels
- **Trading Limits**: Set maximum trades per day
- **Paper/Live Toggle**: Switch between simulation and live trading

## 📁 Project Structure

```
options_autopilot/
├── main.py                 # Main application entry point
├── config.py              # Configuration management
├── gui.py                 # Desktop GUI interface
├── trade_executor.py      # Trade execution and position management
├── requirements.txt       # Python dependencies
├── data/
│   ├── stock_universe.py  # S&P 500 stock filtering
│   └── options_fetcher.py # Options data and pricing
├── strategies/
│   └── ttm_squeeze.py     # TTM Squeeze signal detection
├── utils/
│   └── logger.py          # Logging configuration
└── logs/
    ├── trading.log        # Application logs
    └── trade_log.csv      # Trade history
```

## ⚙️ Configuration

The system creates a `config.json` file with default settings:

### Key Settings

- **Paper Trading**: `true` (recommended for beginners)
- **Max Trades/Day**: `3`
- **Risk per Trade**: `2%` of account
- **Stop Loss**: `50%` of premium
- **Take Profit**: `100%` gain

### API Configuration

- **Alpaca API**: Primary data and execution source
- **Paper Trading URL**: `https://paper-api.alpaca.markets`
- **Live Trading URL**: `https://api.alpaca.markets`

## 📈 Strategy Details

### Signal Types

1. **Bullish Breakout**: Buy Call options
   - TTM Squeeze ending with upward momentum
   - Price above key moving averages
   - Volume surge confirmation

2. **Bearish Breakout**: Buy Put options
   - TTM Squeeze ending with downward momentum
   - Price below key moving averages
   - Volume surge confirmation

3. **Squeeze Building**: Monitor for future breakout
   - Active squeeze condition
   - Building momentum

### Option Selection Criteria

- **Strike Price**: 5-15% out of the money
- **Expiration**: 1-6 weeks (sweet spot for time decay)
- **Volume**: Minimum 100 contracts traded
- **Open Interest**: Minimum 500 contracts
- **Bid-Ask Spread**: Maximum 10 cents

## 🔧 Troubleshooting

### Common Issues

1. **"No API Key" Error**:
   - Check your `.env` file has correct API keys
   - Verify keys are active in your Alpaca account

2. **"No Signals Found"**:
   - Market conditions may not have suitable setups
   - Try running during market hours (9:30 AM - 4:00 PM ET)

3. **GUI Not Loading**:
   - Ensure PySimpleGUI is installed: `pip install PySimpleGUI`
   - Try different theme in settings

### Getting Help

- Check the `logs/trading.log` file for detailed error messages
- Ensure all dependencies are installed correctly
- Verify your Alpaca account has options trading enabled

## 🚨 Important Disclaimers

### Risk Warning

- **Options trading involves substantial risk** and is not suitable for all investors
- **Past performance does not guarantee future results**
- **Never trade with money you cannot afford to lose**
- **This is educational software** - not financial advice

### Recommended Approach

1. **Start with paper trading** to understand the system
2. **Study the signals** and learn the strategy
3. **Begin with small positions** when going live
4. **Monitor trades closely** especially initially
5. **Keep detailed records** for tax and analysis purposes

## 🔮 Future Enhancements

### Planned Features

- **Machine Learning**: Improve signal accuracy with historical data
- **Multiple Strategies**: Add more technical analysis methods
- **Voice Alerts**: Audio notifications for trade signals
- **Mobile App**: iOS/Android companion app
- **Backtesting**: Historical strategy performance analysis

### Advanced Features

- **Portfolio Optimization**: Multi-asset position sizing
- **Earnings Calendar**: Avoid trades around earnings
- **Market Regime Detection**: Adapt strategy to market conditions
- **Social Trading**: Share and follow successful strategies

## 📞 Support

For questions, issues, or suggestions:

1. Check the logs in `logs/trading.log`
2. Review this README for common solutions
3. Ensure you're using the latest version
4. Test with paper trading first

---

**Remember**: This tool is designed to assist with trading decisions, but you are ultimately responsible for all trades. Always understand the risks involved in options trading and never invest more than you can afford to lose.

Happy Trading! 🚀📈
