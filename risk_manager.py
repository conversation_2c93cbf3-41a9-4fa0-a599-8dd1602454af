"""
Advanced Risk Manager for Options Trading
Sophisticated position sizing, correlation analysis, and portfolio risk management
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
import logging
from datetime import datetime, timedelta
from scipy.stats import norm
from scipy.optimize import minimize

from trading_types import StrategyScore, StrategyType, Position, Greeks

class AdvancedRiskManager:
    """Advanced risk management for options portfolio."""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Risk parameters
        self.max_portfolio_heat = 0.15  # 15% max portfolio risk
        self.max_position_size = 0.05   # 5% max per position
        self.max_sector_exposure = 0.25 # 25% max per sector
        self.max_correlation = 0.7      # Max correlation between positions
        
        # Greeks limits
        self.max_portfolio_delta = 100  # Max net delta
        self.max_portfolio_gamma = 50   # Max net gamma
        self.max_portfolio_vega = 200   # Max net vega
        
        # Sector mappings (simplified)
        self.sector_map = self._initialize_sector_map()
        
        # Correlation matrix (would be updated from market data)
        self.correlation_matrix = {}
    
    def filter_strategies(self, symbol: str, strategy_scores: List[StrategyScore],
                         current_positions: Dict[str, List[Position]], 
                         portfolio_heat: float) -> List[StrategyScore]:
        """Filter strategies based on risk constraints."""
        
        approved_strategies = []
        
        for strategy_score in strategy_scores:
            # Check portfolio heat limit
            if portfolio_heat + strategy_score.max_risk > self.max_portfolio_heat:
                self.logger.debug(f"Rejected {strategy_score.strategy.value}: Portfolio heat limit")
                continue
            
            # Check position size limit
            position_size = self._calculate_position_size(strategy_score, symbol)
            if position_size > self.max_position_size:
                self.logger.debug(f"Rejected {strategy_score.strategy.value}: Position size limit")
                continue
            
            # Check sector exposure
            if not self._check_sector_exposure(symbol, position_size, current_positions):
                self.logger.debug(f"Rejected {strategy_score.strategy.value}: Sector exposure limit")
                continue
            
            # Check correlation limits
            if not self._check_correlation_limits(symbol, current_positions):
                self.logger.debug(f"Rejected {strategy_score.strategy.value}: Correlation limit")
                continue
            
            # Check Greeks limits
            estimated_greeks = self._estimate_strategy_greeks(strategy_score, symbol)
            if not self._check_greeks_limits(estimated_greeks, current_positions):
                self.logger.debug(f"Rejected {strategy_score.strategy.value}: Greeks limit")
                continue
            
            # Adjust score based on risk factors
            adjusted_score = self._adjust_score_for_risk(strategy_score, symbol, current_positions)
            approved_strategies.append(adjusted_score)
        
        self.logger.info(f"Approved {len(approved_strategies)}/{len(strategy_scores)} strategies for {symbol}")
        return approved_strategies
    
    def calculate_optimal_position_size(self, strategy_score: StrategyScore, symbol: str,
                                      portfolio_value: float, current_positions: Dict) -> float:
        """Calculate optimal position size using Kelly Criterion and risk constraints."""
        
        # Kelly Criterion
        win_prob = strategy_score.win_probability
        expected_return = strategy_score.expected_return
        max_loss = strategy_score.max_risk
        
        # Kelly fraction
        if max_loss > 0:
            kelly_fraction = (win_prob * expected_return - (1 - win_prob) * max_loss) / max_loss
            kelly_fraction = max(0, min(0.25, kelly_fraction))  # Cap at 25%
        else:
            kelly_fraction = 0.02  # Default 2%
        
        # Risk parity adjustment
        portfolio_volatility = self._calculate_portfolio_volatility(current_positions)
        strategy_volatility = self._estimate_strategy_volatility(strategy_score)
        
        if portfolio_volatility > 0:
            risk_parity_weight = 0.15 / strategy_volatility  # Target 15% vol contribution
        else:
            risk_parity_weight = 0.05
        
        # Combine Kelly and risk parity
        optimal_size = (kelly_fraction * 0.6 + risk_parity_weight * 0.4)
        
        # Apply hard limits
        optimal_size = min(optimal_size, self.max_position_size)
        
        # Check available capital
        available_capital = self._calculate_available_capital(portfolio_value, current_positions)
        max_affordable = available_capital * 0.8  # Keep 20% buffer
        
        if optimal_size * portfolio_value > max_affordable:
            optimal_size = max_affordable / portfolio_value
        
        return optimal_size
    
    def calculate_hedge_requirements(self, current_positions: Dict[str, List[Position]]) -> Dict[str, Any]:
        """Calculate hedging requirements for portfolio."""
        
        # Calculate net Greeks
        net_greeks = self._calculate_net_greeks(current_positions)
        
        hedge_requirements = {
            'delta_hedge': 0,
            'gamma_hedge': 0,
            'vega_hedge': 0,
            'hedge_instruments': []
        }
        
        # Delta hedging
        if abs(net_greeks.delta) > self.max_portfolio_delta:
            hedge_requirements['delta_hedge'] = -net_greeks.delta
            hedge_requirements['hedge_instruments'].append({
                'type': 'stock',
                'quantity': int(-net_greeks.delta / 100),  # Convert to shares
                'purpose': 'delta_neutral'
            })
        
        # Gamma hedging
        if abs(net_greeks.gamma) > self.max_portfolio_gamma:
            hedge_requirements['gamma_hedge'] = -net_greeks.gamma
            hedge_requirements['hedge_instruments'].append({
                'type': 'long_options',
                'quantity': int(abs(net_greeks.gamma) / 10),
                'purpose': 'gamma_hedge'
            })
        
        # Vega hedging
        if abs(net_greeks.vega) > self.max_portfolio_vega:
            hedge_requirements['vega_hedge'] = -net_greeks.vega
            hedge_requirements['hedge_instruments'].append({
                'type': 'calendar_spread',
                'quantity': int(abs(net_greeks.vega) / 20),
                'purpose': 'vega_hedge'
            })
        
        return hedge_requirements
    
    def assess_portfolio_risk(self, current_positions: Dict[str, List[Position]]) -> Dict[str, Any]:
        """Comprehensive portfolio risk assessment."""
        
        # Calculate VaR (Value at Risk)
        portfolio_value = self._calculate_portfolio_value(current_positions)
        daily_var_95 = self._calculate_var(current_positions, confidence=0.95)
        daily_var_99 = self._calculate_var(current_positions, confidence=0.99)
        
        # Calculate maximum drawdown potential
        max_drawdown = self._calculate_max_drawdown_potential(current_positions)
        
        # Concentration risk
        concentration_risk = self._calculate_concentration_risk(current_positions)
        
        # Liquidity risk
        liquidity_risk = self._calculate_liquidity_risk(current_positions)
        
        # Greeks risk
        greeks_risk = self._assess_greeks_risk(current_positions)
        
        return {
            'portfolio_value': portfolio_value,
            'daily_var_95': daily_var_95,
            'daily_var_99': daily_var_99,
            'max_drawdown_potential': max_drawdown,
            'concentration_risk': concentration_risk,
            'liquidity_risk': liquidity_risk,
            'greeks_risk': greeks_risk,
            'overall_risk_score': self._calculate_overall_risk_score(
                daily_var_95, max_drawdown, concentration_risk, liquidity_risk
            )
        }
    
    def _calculate_position_size(self, strategy_score: StrategyScore, symbol: str) -> float:
        """Calculate position size as percentage of portfolio."""
        base_size = 0.02  # 2% base
        
        # Adjust for strategy confidence
        confidence_multiplier = strategy_score.confidence
        
        # Adjust for expected return/risk ratio
        if strategy_score.max_risk > 0:
            risk_adjusted_size = base_size * (strategy_score.expected_return / strategy_score.max_risk)
        else:
            risk_adjusted_size = base_size
        
        return min(self.max_position_size, risk_adjusted_size * confidence_multiplier)
    
    def _check_sector_exposure(self, symbol: str, position_size: float,
                              current_positions: Dict[str, List[Position]]) -> bool:
        """Check if adding position would exceed sector exposure limits."""
        
        symbol_sector = self.sector_map.get(symbol, 'Unknown')
        current_sector_exposure = 0
        
        for sym, positions in current_positions.items():
            if self.sector_map.get(sym, 'Unknown') == symbol_sector:
                for pos in positions:
                    if pos.status == 'open':
                        current_sector_exposure += abs(pos.total_cost)
        
        # Estimate new position cost (simplified)
        estimated_cost = position_size * 10000  # Assume $10k portfolio
        
        return (current_sector_exposure + estimated_cost) / 10000 <= self.max_sector_exposure
    
    def _check_correlation_limits(self, symbol: str, current_positions: Dict[str, List[Position]]) -> bool:
        """Check correlation limits with existing positions."""
        
        for existing_symbol in current_positions.keys():
            if existing_symbol == symbol:
                continue
            
            correlation = self._get_correlation(symbol, existing_symbol)
            if abs(correlation) > self.max_correlation:
                return False
        
        return True
    
    def _estimate_strategy_greeks(self, strategy_score: StrategyScore, symbol: str) -> Greeks:
        """Estimate Greeks for a strategy."""
        # Simplified Greeks estimation
        strategy_type = strategy_score.strategy
        
        # Base Greeks by strategy type
        greeks_map = {
            StrategyType.LONG_CALL: Greeks(0.5, 0.1, -0.05, 0.2, 0.01),
            StrategyType.LONG_PUT: Greeks(-0.5, 0.1, -0.05, 0.2, -0.01),
            StrategyType.IRON_CONDOR: Greeks(0.0, -0.05, 0.1, -0.1, 0.0),
            StrategyType.COVERED_CALL: Greeks(0.7, 0.0, 0.03, -0.1, 0.01),
            # ... other strategies
        }
        
        return greeks_map.get(strategy_type, Greeks(0, 0, 0, 0, 0))
    
    def _check_greeks_limits(self, estimated_greeks: Greeks, 
                           current_positions: Dict[str, List[Position]]) -> bool:
        """Check if adding position would exceed Greeks limits."""
        
        current_greeks = self._calculate_net_greeks(current_positions)
        
        new_delta = current_greeks.delta + estimated_greeks.delta
        new_gamma = current_greeks.gamma + estimated_greeks.gamma
        new_vega = current_greeks.vega + estimated_greeks.vega
        
        return (abs(new_delta) <= self.max_portfolio_delta and
                abs(new_gamma) <= self.max_portfolio_gamma and
                abs(new_vega) <= self.max_portfolio_vega)
    
    def _calculate_net_greeks(self, current_positions: Dict[str, List[Position]]) -> Greeks:
        """Calculate net portfolio Greeks."""
        total_delta = total_gamma = total_theta = total_vega = total_rho = 0
        
        for positions in current_positions.values():
            for pos in positions:
                if pos.status == 'open':
                    total_delta += pos.greeks.delta
                    total_gamma += pos.greeks.gamma
                    total_theta += pos.greeks.theta
                    total_vega += pos.greeks.vega
                    total_rho += pos.greeks.rho
        
        return Greeks(total_delta, total_gamma, total_theta, total_vega, total_rho)
    
    def _calculate_var(self, current_positions: Dict[str, List[Position]], confidence: float) -> float:
        """Calculate Value at Risk."""
        # Simplified VaR calculation
        portfolio_value = self._calculate_portfolio_value(current_positions)
        portfolio_volatility = self._calculate_portfolio_volatility(current_positions)
        
        # Normal distribution assumption
        z_score = norm.ppf(1 - confidence)
        var = portfolio_value * portfolio_volatility * z_score * np.sqrt(1/252)  # Daily VaR
        
        return abs(var)
    
    def _calculate_portfolio_value(self, current_positions: Dict[str, List[Position]]) -> float:
        """Calculate total portfolio value."""
        total_value = 0
        for positions in current_positions.values():
            for pos in positions:
                if pos.status == 'open':
                    total_value += abs(pos.total_cost)
        return total_value
    
    def _calculate_portfolio_volatility(self, current_positions: Dict[str, List[Position]]) -> float:
        """Calculate portfolio volatility."""
        # Simplified - would use correlation matrix in production
        return 0.20  # 20% annualized volatility assumption
    
    def _get_correlation(self, symbol1: str, symbol2: str) -> float:
        """Get correlation between two symbols."""
        # Simplified - would use actual correlation data
        if symbol1 == symbol2:
            return 1.0
        
        # Same sector correlation
        if (self.sector_map.get(symbol1) == self.sector_map.get(symbol2) and
            self.sector_map.get(symbol1) != 'Unknown'):
            return 0.6
        
        return 0.3  # Default market correlation
    
    def _initialize_sector_map(self) -> Dict[str, str]:
        """Initialize sector mapping for symbols."""
        return {
            'AAPL': 'Technology',
            'MSFT': 'Technology',
            'GOOGL': 'Technology',
            'AMZN': 'Consumer Discretionary',
            'TSLA': 'Consumer Discretionary',
            'NVDA': 'Technology',
            'META': 'Technology',
            'JPM': 'Financials',
            'BAC': 'Financials',
            'XOM': 'Energy',
            # ... more mappings
        }
    
    def _adjust_score_for_risk(self, strategy_score: StrategyScore, symbol: str,
                              current_positions: Dict) -> StrategyScore:
        """Adjust strategy score based on risk factors."""
        
        # Risk adjustment factors
        concentration_penalty = self._calculate_concentration_penalty(symbol, current_positions)
        correlation_penalty = self._calculate_correlation_penalty(symbol, current_positions)
        
        # Apply penalties
        adjusted_score = strategy_score.score * (1 - concentration_penalty - correlation_penalty)
        
        # Create new StrategyScore with adjusted score
        return StrategyScore(
            strategy=strategy_score.strategy,
            score=max(0, adjusted_score),
            confidence=strategy_score.confidence,
            expected_return=strategy_score.expected_return,
            max_risk=strategy_score.max_risk,
            win_probability=strategy_score.win_probability,
            reasoning=strategy_score.reasoning + f" (Risk-adjusted: -{concentration_penalty:.1%}-{correlation_penalty:.1%})"
        )
    
    def _calculate_concentration_penalty(self, symbol: str, current_positions: Dict) -> float:
        """Calculate penalty for concentration risk."""
        if symbol in current_positions:
            return 0.1  # 10% penalty for existing position
        return 0
    
    def _calculate_correlation_penalty(self, symbol: str, current_positions: Dict) -> float:
        """Calculate penalty for high correlation."""
        max_correlation = 0
        for existing_symbol in current_positions.keys():
            correlation = abs(self._get_correlation(symbol, existing_symbol))
            max_correlation = max(max_correlation, correlation)
        
        if max_correlation > 0.7:
            return 0.15  # 15% penalty for high correlation
        elif max_correlation > 0.5:
            return 0.05  # 5% penalty for moderate correlation
        
        return 0
