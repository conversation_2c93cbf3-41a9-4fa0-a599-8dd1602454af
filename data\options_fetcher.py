"""
Options Data Fetcher - Real-time options chains via Alpaca
"""

import logging
import pandas as pd
from datetime import datetime, timedelta
from typing import List, Dict, Optional, Tuple
import alpaca_trade_api as tradeapi
import asyncio
from dataclasses import dataclass

@dataclass
class OptionContract:
    """Represents an option contract."""
    symbol: str
    underlying: str
    strike: float
    expiry: datetime
    option_type: str  # 'call' or 'put'
    bid: float
    ask: float
    last: float
    volume: int
    open_interest: int
    implied_volatility: float
    delta: float
    gamma: float
    theta: float
    vega: float
    
    @property
    def mid_price(self) -> float:
        """Calculate mid price between bid and ask."""
        if self.bid > 0 and self.ask > 0:
            return (self.bid + self.ask) / 2
        return self.last if self.last > 0 else 0
    
    @property
    def spread(self) -> float:
        """Calculate bid-ask spread."""
        if self.bid > 0 and self.ask > 0:
            return self.ask - self.bid
        return 0
    
    @property
    def spread_percent(self) -> float:
        """Calculate spread as percentage of mid price."""
        mid = self.mid_price
        if mid > 0:
            return (self.spread / mid) * 100
        return 0

class OptionsDataFetcher:
    """Fetches real-time options data from Alpaca."""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize Alpaca API
        self.api = tradeapi.REST(
            key_id=config.alpaca_api_key,
            secret_key=config.alpaca_secret_key,
            base_url=config.alpaca_base_url,
            api_version='v2'
        )
        
        self.option_chains = {}
        self.last_update = {}
    
    async def get_option_chain(self, symbol: str, refresh: bool = False) -> Dict[str, List[OptionContract]]:
        """Get option chain for a symbol."""
        try:
            # Check if we need to refresh data
            if not refresh and symbol in self.option_chains:
                last_update = self.last_update.get(symbol, datetime.min)
                if (datetime.now() - last_update).seconds < 300:  # 5 minutes cache
                    return self.option_chains[symbol]
            
            # Fetch option chain from Alpaca
            # Note: This is a simplified version - actual implementation would depend on Alpaca's options API
            option_chain = await self._fetch_alpaca_options(symbol)
            
            if option_chain:
                self.option_chains[symbol] = option_chain
                self.last_update[symbol] = datetime.now()
                self.logger.debug(f"Updated option chain for {symbol}")
            
            return option_chain
            
        except Exception as e:
            self.logger.error(f"Error fetching option chain for {symbol}: {e}")
            return {'calls': [], 'puts': []}
    
    async def _fetch_alpaca_options(self, symbol: str) -> Dict[str, List[OptionContract]]:
        """Fetch options data from Alpaca API."""
        try:
            # This is a placeholder - actual implementation would use Alpaca's options endpoints
            # For now, we'll simulate option data based on current stock price
            
            # Get current stock price
            quote = self.api.get_latest_quote(symbol)
            current_price = float(quote.ask_price + quote.bid_price) / 2
            
            calls = []
            puts = []
            
            # Generate option strikes around current price
            strikes = self._generate_strikes(current_price)
            expiries = self._get_option_expiries()
            
            for expiry in expiries:
                for strike in strikes:
                    # Simulate call option
                    call_contract = self._simulate_option_contract(
                        symbol, strike, expiry, 'call', current_price
                    )
                    if call_contract:
                        calls.append(call_contract)
                    
                    # Simulate put option
                    put_contract = self._simulate_option_contract(
                        symbol, strike, expiry, 'put', current_price
                    )
                    if put_contract:
                        puts.append(put_contract)
            
            return {'calls': calls, 'puts': puts}
            
        except Exception as e:
            self.logger.error(f"Error in _fetch_alpaca_options for {symbol}: {e}")
            return {'calls': [], 'puts': []}
    
    def _generate_strikes(self, current_price: float) -> List[float]:
        """Generate option strikes around current price."""
        strikes = []
        
        # Generate strikes from 20% below to 20% above current price
        start_price = current_price * 0.8
        end_price = current_price * 1.2
        
        # Determine strike interval based on price
        if current_price < 50:
            interval = 2.5
        elif current_price < 100:
            interval = 5
        elif current_price < 200:
            interval = 10
        else:
            interval = 20
        
        strike = round(start_price / interval) * interval
        while strike <= end_price:
            strikes.append(strike)
            strike += interval
        
        return strikes
    
    def _get_option_expiries(self) -> List[datetime]:
        """Get upcoming option expiration dates."""
        expiries = []
        today = datetime.now().date()
        
        # Find next few Fridays (typical option expiry)
        current_date = today
        fridays_found = 0
        
        while fridays_found < 8:  # Get next 8 expiries
            if current_date.weekday() == 4:  # Friday
                expiry_datetime = datetime.combine(current_date, datetime.min.time())
                expiries.append(expiry_datetime)
                fridays_found += 1
            current_date += timedelta(days=1)
        
        return expiries
    
    def _simulate_option_contract(self, underlying: str, strike: float, expiry: datetime, 
                                option_type: str, current_price: float) -> Optional[OptionContract]:
        """Simulate an option contract with realistic pricing."""
        try:
            # Calculate days to expiry
            days_to_expiry = (expiry.date() - datetime.now().date()).days
            if days_to_expiry <= 0:
                return None
            
            # Simple Black-Scholes approximation for simulation
            time_value = days_to_expiry / 365.0
            volatility = 0.25  # Assume 25% IV
            
            # Calculate intrinsic value
            if option_type == 'call':
                intrinsic = max(0, current_price - strike)
                moneyness = current_price / strike
            else:  # put
                intrinsic = max(0, strike - current_price)
                moneyness = strike / current_price
            
            # Estimate time value (simplified)
            time_premium = current_price * volatility * (time_value ** 0.5) * 0.4
            
            # Adjust time premium based on moneyness
            if 0.95 <= moneyness <= 1.05:  # ATM
                time_premium *= 1.0
            elif 0.9 <= moneyness <= 1.1:  # Near ATM
                time_premium *= 0.8
            else:  # Far OTM/ITM
                time_premium *= 0.5
            
            theoretical_price = intrinsic + time_premium
            
            # Add bid-ask spread
            spread = max(0.05, theoretical_price * 0.05)  # 5% spread minimum 5 cents
            bid = max(0.01, theoretical_price - spread/2)
            ask = theoretical_price + spread/2
            
            # Simulate volume and open interest
            volume = max(1, int(100 * (1.1 - abs(moneyness - 1)) * 10))
            open_interest = max(10, volume * 5)
            
            # Create option symbol
            expiry_str = expiry.strftime("%y%m%d")
            option_symbol = f"{underlying}{expiry_str}{'C' if option_type == 'call' else 'P'}{int(strike*1000):08d}"
            
            return OptionContract(
                symbol=option_symbol,
                underlying=underlying,
                strike=strike,
                expiry=expiry,
                option_type=option_type,
                bid=round(bid, 2),
                ask=round(ask, 2),
                last=round(theoretical_price, 2),
                volume=volume,
                open_interest=open_interest,
                implied_volatility=volatility,
                delta=0.5 if moneyness == 1 else (0.7 if option_type == 'call' and moneyness > 1 else 0.3),
                gamma=0.1,
                theta=-0.05,
                vega=0.2
            )
            
        except Exception as e:
            self.logger.error(f"Error simulating option contract: {e}")
            return None
    
    def find_best_contracts(self, symbol: str, option_type: str, 
                          target_dte: int = 30) -> List[OptionContract]:
        """Find the best option contracts based on criteria."""
        try:
            chain = self.option_chains.get(symbol, {'calls': [], 'puts': []})
            contracts = chain.get('calls' if option_type == 'call' else 'puts', [])
            
            if not contracts:
                return []
            
            # Filter by criteria
            min_volume = self.config.get('screening.min_option_volume', 100)
            min_oi = self.config.get('screening.min_open_interest', 500)
            max_spread = self.config.get('screening.max_bid_ask_spread', 0.10)
            
            filtered_contracts = []
            for contract in contracts:
                # Check days to expiry
                days_to_expiry = (contract.expiry.date() - datetime.now().date()).days
                if not (7 <= days_to_expiry <= 45):  # 1 week to 6 weeks
                    continue
                
                # Check liquidity
                if (contract.volume >= min_volume and 
                    contract.open_interest >= min_oi and
                    contract.spread <= max_spread):
                    filtered_contracts.append(contract)
            
            # Sort by volume and open interest
            filtered_contracts.sort(
                key=lambda x: (x.volume + x.open_interest), 
                reverse=True
            )
            
            return filtered_contracts[:10]  # Return top 10
            
        except Exception as e:
            self.logger.error(f"Error finding best contracts for {symbol}: {e}")
            return []
