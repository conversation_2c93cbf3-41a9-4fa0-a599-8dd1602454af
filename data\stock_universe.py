"""
Stock Universe Management - S&P 500 and filtering
"""

import pandas as pd
import requests
import logging
import os
from typing import List, Dict, Optional
import yfinance as yf
from datetime import datetime, timedelta
import asyncio
import aiohttp

class StockUniverse:
    """Manages the stock universe for options trading."""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.sp500_symbols = []
        self.filtered_symbols = []
        self.symbol_data = {}
        
    async def load_sp500_symbols(self) -> List[str]:
        """Load S&P 500 symbols using FMP API or fallback methods."""
        try:
            # Try FMP API first
            fmp_api_key = os.getenv('FMP_API_KEY')
            if fmp_api_key:
                symbols = await self._load_from_fmp(fmp_api_key)
                if symbols:
                    self.sp500_symbols = symbols
                    self.logger.info(f"Loaded {len(symbols)} S&P 500 symbols from FMP API")
                    return symbols

            # Fallback to Wikipedia
            url = "https://en.wikipedia.org/wiki/List_of_S%26P_500_companies"
            tables = pd.read_html(url)
            sp500_table = tables[0]

            # Extract symbols and clean them
            symbols = sp500_table['Symbol'].tolist()
            # Replace dots with dashes for Yahoo Finance compatibility
            symbols = [symbol.replace('.', '-') for symbol in symbols]

            self.sp500_symbols = symbols
            self.logger.info(f"Loaded {len(symbols)} S&P 500 symbols from Wikipedia")
            return symbols

        except Exception as e:
            self.logger.error(f"Error loading S&P 500 symbols: {e}")
            # Fallback to a curated list of popular symbols
            fallback_symbols = [
                'AAPL', 'MSFT', 'GOOGL', 'AMZN', 'TSLA', 'META', 'NVDA', 'NFLX',
                'AMD', 'INTC', 'CRM', 'ORCL', 'ADBE', 'PYPL', 'UBER', 'LYFT',
                'SPY', 'QQQ', 'IWM', 'DIA', 'XLF', 'XLE', 'XLK', 'XLV',
                'JPM', 'BAC', 'WFC', 'GS', 'MS', 'C', 'V', 'MA', 'PYPL',
                'JNJ', 'PFE', 'UNH', 'ABBV', 'MRK', 'TMO', 'DHR', 'ABT'
            ]
            self.sp500_symbols = fallback_symbols
            self.logger.warning(f"Using fallback symbols: {len(fallback_symbols)} symbols")
            return fallback_symbols

    async def _load_from_fmp(self, api_key: str) -> List[str]:
        """Load S&P 500 symbols from Financial Modeling Prep API."""
        try:
            url = f"https://financialmodelingprep.com/api/v3/sp500_constituent?apikey={api_key}"

            async with aiohttp.ClientSession() as session:
                async with session.get(url) as response:
                    if response.status == 200:
                        data = await response.json()
                        symbols = [item['symbol'] for item in data if 'symbol' in item]
                        return symbols[:500]  # Limit to 500 symbols
                    else:
                        self.logger.warning(f"FMP API returned status {response.status}")
                        return []
        except Exception as e:
            self.logger.warning(f"Error loading from FMP API: {e}")
            return []
    
    async def get_symbol_info(self, symbol: str) -> Optional[Dict]:
        """Get basic info for a symbol using yfinance."""
        try:
            ticker = yf.Ticker(symbol)
            info = ticker.info
            
            # Get recent price data
            hist = ticker.history(period="5d")
            if hist.empty:
                return None
            
            latest_data = hist.iloc[-1]
            
            return {
                'symbol': symbol,
                'market_cap': info.get('marketCap', 0),
                'volume': latest_data['Volume'],
                'price': latest_data['Close'],
                'sector': info.get('sector', 'Unknown'),
                'industry': info.get('industry', 'Unknown'),
                'avg_volume': info.get('averageVolume', 0),
                'beta': info.get('beta', 1.0),
                'pe_ratio': info.get('trailingPE', 0),
                'forward_pe': info.get('forwardPE', 0)
            }
            
        except Exception as e:
            self.logger.debug(f"Error getting info for {symbol}: {e}")
            return None
    
    async def filter_symbols(self, symbols: List[str]) -> List[str]:
        """Filter symbols based on market cap, volume, and other criteria."""
        self.logger.info(f"Filtering {len(symbols)} symbols...")
        
        min_market_cap = self.config.get('screening.min_market_cap', 10_000_000_000)
        min_volume = self.config.get('screening.min_volume', 1_000_000)
        
        filtered_symbols = []
        symbol_info_tasks = []
        
        # Create tasks for concurrent processing
        semaphore = asyncio.Semaphore(10)  # Limit concurrent requests
        
        async def get_info_with_semaphore(symbol):
            async with semaphore:
                return await self.get_symbol_info(symbol)
        
        # Process symbols in batches
        batch_size = 50
        for i in range(0, len(symbols), batch_size):
            batch = symbols[i:i + batch_size]
            tasks = [get_info_with_semaphore(symbol) for symbol in batch]
            results = await asyncio.gather(*tasks, return_exceptions=True)
            
            for symbol, result in zip(batch, results):
                if isinstance(result, Exception):
                    self.logger.debug(f"Exception for {symbol}: {result}")
                    continue
                    
                if result is None:
                    continue
                
                # Apply filters
                if (result['market_cap'] >= min_market_cap and 
                    result['volume'] >= min_volume and
                    result['price'] > 5.0):  # Minimum price filter
                    
                    filtered_symbols.append(symbol)
                    self.symbol_data[symbol] = result
            
            # Add small delay between batches
            await asyncio.sleep(0.1)
        
        self.filtered_symbols = filtered_symbols
        self.logger.info(f"Filtered to {len(filtered_symbols)} symbols meeting criteria")
        return filtered_symbols
    
    async def update_universe(self) -> List[str]:
        """Update the entire stock universe."""
        try:
            # Load S&P 500 symbols
            symbols = await self.load_sp500_symbols()
            
            # Filter symbols
            filtered = await self.filter_symbols(symbols)
            
            return filtered
            
        except Exception as e:
            self.logger.error(f"Error updating stock universe: {e}")
            return []
    
    def get_symbol_data(self, symbol: str) -> Optional[Dict]:
        """Get cached symbol data."""
        return self.symbol_data.get(symbol)
    
    def get_filtered_symbols(self) -> List[str]:
        """Get the current filtered symbol list."""
        return self.filtered_symbols.copy()
    
    def get_symbols_by_sector(self, sector: str) -> List[str]:
        """Get symbols filtered by sector."""
        return [
            symbol for symbol, data in self.symbol_data.items()
            if data.get('sector', '').lower() == sector.lower()
        ]
