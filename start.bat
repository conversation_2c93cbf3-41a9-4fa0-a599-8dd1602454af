@echo off
echo 🚀 AutoPilot Options Trader - Quick Start
echo ========================================

REM Check if Python is installed
python --version >nul 2>&1
if errorlevel 1 (
    echo ❌ Python is not installed or not in PATH
    echo Please install Python 3.8+ from https://python.org
    pause
    exit /b 1
)

echo ✅ Python found

REM Check if virtual environment exists
if not exist "venv" (
    echo 📦 Creating virtual environment...
    python -m venv venv
    if errorlevel 1 (
        echo ❌ Failed to create virtual environment
        pause
        exit /b 1
    )
)

REM Activate virtual environment
echo 🔧 Activating virtual environment...
call venv\Scripts\activate.bat

REM Install requirements if needed
if not exist "venv\Lib\site-packages\PySimpleGUI" (
    echo 📦 Installing requirements...
    pip install -r requirements.txt
    if errorlevel 1 (
        echo ❌ Failed to install requirements
        pause
        exit /b 1
    )
)

REM Check if .env file exists
if not exist ".env" (
    echo ⚠️  .env file not found
    if exist ".env.example" (
        echo 📝 Copying .env.example to .env
        copy .env.example .env
        echo.
        echo ⚠️  IMPORTANT: Please edit .env file with your Alpaca API keys
        echo    Then run this script again
        pause
        exit /b 0
    ) else (
        echo ❌ No .env.example file found
        pause
        exit /b 1
    )
)

REM Run the launcher
echo 🚀 Starting AutoPilot Options Trader Launcher...
python launch_gui.py

REM Keep window open if there's an error
if errorlevel 1 (
    echo.
    echo ❌ Application exited with an error
    pause
)

deactivate
