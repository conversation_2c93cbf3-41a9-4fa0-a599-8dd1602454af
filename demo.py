#!/usr/bin/env python3
"""
Demo script for AutoPilot Options Trader
Shows the system working without requiring API keys
"""

import sys
import asyncio
import logging
from datetime import datetime
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from config import Config
from utils.logger import setup_logging
from data.stock_universe import StockUniverse
from strategies.ttm_squeeze import TTMSqueezeScreener

async def demo_screening():
    """Demo the screening functionality."""
    print("🚀 AutoPilot Options Trader - Demo Mode")
    print("=" * 50)
    
    # Setup
    setup_logging(log_level="INFO")
    config = Config()
    
    print("📊 Initializing components...")
    universe = StockUniverse(config)
    screener = TTMSqueezeScreener(config)
    
    # Load stock universe
    print("\n📈 Loading stock universe...")
    symbols = await universe.load_sp500_symbols()
    print(f"✅ Loaded {len(symbols)} symbols")
    
    # Show sample symbols
    print(f"📋 Sample symbols: {symbols[:10]}")
    
    # Run screening on a subset
    print(f"\n🔍 Screening top {min(10, len(symbols))} symbols for TTM Squeeze signals...")
    test_symbols = symbols[:10]
    
    signals = await screener.scan_symbols(test_symbols)
    
    print(f"\n📊 Screening Results:")
    print(f"   Symbols scanned: {len(test_symbols)}")
    print(f"   Signals found: {len(signals)}")
    
    if signals:
        print(f"\n🎯 Top Signals:")
        for i, signal in enumerate(signals[:5], 1):
            print(f"   {i}. {signal.symbol}")
            print(f"      Signal: {signal.signal_type}")
            print(f"      Strength: {signal.strength:.2f}")
            print(f"      Price: ${signal.current_price:.2f}")
            print(f"      RSI: {signal.rsi:.1f}")
            print(f"      Notes: {signal.notes}")
            print()
    else:
        print("   No strong signals found in current market conditions")
        print("   This is normal - the system is selective and waits for high-probability setups")
    
    print("\n💡 Demo Complete!")
    print("   In live mode, the system would:")
    print("   1. Find the best option contracts for these signals")
    print("   2. Calculate position sizes based on risk management")
    print("   3. Execute trades automatically")
    print("   4. Monitor positions and manage exits")

def demo_gui():
    """Demo the GUI without full functionality."""
    try:
        import PySimpleGUI as sg
        
        print("\n🖥️  GUI Demo")
        print("=" * 30)
        
        # Simple demo window
        layout = [
            [sg.Text("🤖 AutoPilot Options Trader - Demo", font=("Arial", 16, "bold"))],
            [sg.Text("This is a demo of the GUI interface")],
            [sg.HSeparator()],
            [sg.Text("In the full version, you would see:")],
            [sg.Text("• Real-time market scanning")],
            [sg.Text("• Live trading signals")],
            [sg.Text("• Position monitoring")],
            [sg.Text("• P&L tracking")],
            [sg.HSeparator()],
            [sg.Button("Close Demo", key="-CLOSE-")]
        ]
        
        window = sg.Window("AutoPilot Options Trader - Demo", layout, size=(400, 300))
        
        print("✅ GUI demo window opened")
        print("   Close the window to continue...")
        
        while True:
            event, values = window.read()
            if event in (sg.WIN_CLOSED, "-CLOSE-"):
                break
        
        window.close()
        print("✅ GUI demo completed")
        
    except ImportError:
        print("❌ PySimpleGUI not available for GUI demo")
    except Exception as e:
        print(f"❌ GUI demo error: {e}")

async def main():
    """Main demo function."""
    try:
        print("🎬 Starting AutoPilot Options Trader Demo")
        print("   This demo shows the system working without API keys")
        print()
        
        # Run screening demo
        await demo_screening()
        
        # Show GUI demo
        demo_gui()
        
        print("\n🎉 Demo completed successfully!")
        print("\n📋 Next Steps:")
        print("1. Get Alpaca API keys (free paper trading account)")
        print("2. Set up .env file with your API keys")
        print("3. Run: python main.py")
        print("4. Click 'Scan & Trade' to start live trading")
        
        print("\n⚠️  Important Notes:")
        print("• Always start with paper trading")
        print("• Understand the risks of options trading")
        print("• This is educational software, not financial advice")
        
    except KeyboardInterrupt:
        print("\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n💥 Demo error: {e}")
        logging.error(f"Demo error: {e}", exc_info=True)

if __name__ == "__main__":
    asyncio.run(main())
