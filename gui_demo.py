#!/usr/bin/env python3
"""
GUI Demo for AutoPilot Options Trader
Shows the actual desktop interface working
"""

import PySimpleGUI as sg
import threading
import time
import random
from datetime import datetime

class TradingGUIDemo:
    """Demo version of the trading GUI that actually works."""
    
    def __init__(self):
        # Set theme
        sg.theme('DarkBlue3')
        
        # Demo data
        self.scanning = False
        self.signals = []
        self.positions = []
        self.trades_count = 0
        self.pnl = 0.0
        self.last_scan = "Never"
        
        # Create sample data
        self.create_sample_data()
    
    def create_sample_data(self):
        """Create sample trading data for demo."""
        # Sample signals
        self.signals = [
            ["AAPL", "Bullish Breakout", "0.85", "$175.50", "TTM Squeeze ending; Strong momentum"],
            ["TSLA", "Bearish Breakout", "0.72", "$245.30", "Volume surge; RSI overbought"],
            ["MSFT", "Squeeze Building", "0.65", "$380.20", "Bollinger bands contracting"],
            ["GOOGL", "Bullish Breakout", "0.78", "$142.80", "Breaking above resistance"],
            ["NVDA", "Bearish Breakout", "0.69", "$875.40", "Momentum turning negative"]
        ]
        
        # Sample positions
        self.positions = [
            ["AAPL", "Call $180", "2", "$3.50", "$4.20", "+$140", "Open"],
            ["TSLA", "Put $240", "1", "$5.80", "$7.30", "+$150", "Open"],
            ["MSFT", "Call $385", "3", "$2.10", "$1.80", "-$90", "Open"]
        ]
        
        self.trades_count = 3
        self.pnl = 200.00
    
    def create_layout(self):
        """Create the main GUI layout."""
        
        # Header section
        header_layout = [
            [sg.Text("🤖 AutoPilot Options Trader", font=("Arial", 20, "bold"), justification='center')],
            [sg.Text("AI-Powered Options Trading for Beginners", font=("Arial", 12), justification='center')],
            [sg.HSeparator()]
        ]
        
        # Control section
        control_layout = [
            [sg.Text("Trading Controls", font=("Arial", 14, "bold"))],
            [
                sg.Button("🔍 Scan & Trade", key="-SCAN-", size=(15, 2), 
                         button_color=("white", "green"), font=("Arial", 12, "bold")),
                sg.Button("⏹️ Stop", key="-STOP-", size=(10, 2), 
                         button_color=("white", "red"), disabled=True),
                sg.Button("⚙️ Settings", key="-SETTINGS-", size=(10, 2))
            ],
            [sg.Text("Ready to scan for opportunities", key="-STATUS-", font=("Arial", 10), text_color="blue")]
        ]
        
        # Statistics section
        stats_layout = [
            [sg.Text("Today's Statistics", font=("Arial", 14, "bold"))],
            [
                sg.Text("Trades:", size=(10, 1)), 
                sg.Text(str(self.trades_count), key="-TRADES-COUNT-", size=(5, 1)),
                sg.Text("P&L:", size=(10, 1)), 
                sg.Text(f"${self.pnl:.2f}", key="-PNL-", size=(10, 1), text_color="green")
            ],
            [
                sg.Text("Win Rate:", size=(10, 1)), 
                sg.Text("67%", key="-WIN-RATE-", size=(5, 1)),
                sg.Text("Last Scan:", size=(10, 1)), 
                sg.Text(self.last_scan, key="-LAST-SCAN-", size=(15, 1))
            ]
        ]
        
        # Signals section
        signals_layout = [
            [sg.Text("Current Signals", font=("Arial", 14, "bold"))],
            [
                sg.Table(
                    values=self.signals,
                    headings=["Symbol", "Signal", "Strength", "Price", "Notes"],
                    key="-SIGNALS-TABLE-",
                    auto_size_columns=True,
                    justification="left",
                    num_rows=6,
                    alternating_row_color="lightblue",
                    enable_events=True,
                    col_widths=[8, 15, 8, 10, 30]
                )
            ]
        ]
        
        # Positions section
        positions_layout = [
            [sg.Text("Open Positions", font=("Arial", 14, "bold"))],
            [
                sg.Table(
                    values=self.positions,
                    headings=["Symbol", "Type", "Qty", "Entry", "Current", "P&L", "Status"],
                    key="-POSITIONS-TABLE-",
                    auto_size_columns=True,
                    justification="left",
                    num_rows=4,
                    alternating_row_color="lightgray",
                    col_widths=[8, 12, 5, 8, 8, 10, 8]
                )
            ]
        ]
        
        # Log section
        log_layout = [
            [sg.Text("Activity Log", font=("Arial", 14, "bold"))],
            [
                sg.Multiline(
                    default_text="AutoPilot Options Trader initialized...\n[Demo Mode] - No real trades will be executed\nReady to scan for trading opportunities\n",
                    key="-LOG-",
                    size=(80, 6),
                    autoscroll=True,
                    disabled=True,
                    font=("Courier", 9)
                )
            ]
        ]
        
        # Main layout with columns
        left_column = [
            [sg.Column(control_layout, vertical_alignment="top")],
            [sg.HSeparator()],
            [sg.Column(signals_layout, expand_x=True)],
            [sg.HSeparator()],
            [sg.Column(positions_layout, expand_x=True)]
        ]
        
        right_column = [
            [sg.Column(stats_layout, vertical_alignment="top")],
            [sg.HSeparator()],
            [sg.Column(log_layout, expand_x=True, expand_y=True)]
        ]
        
        layout = [
            [sg.Column(header_layout, justification="center", expand_x=True)],
            [sg.HSeparator()],
            [
                sg.Column(left_column, vertical_alignment="top", expand_x=True),
                sg.VSeparator(),
                sg.Column(right_column, vertical_alignment="top", expand_x=True)
            ]
        ]
        
        return layout
    
    def start_scan_demo(self):
        """Demo the scanning process."""
        if self.scanning:
            return
            
        self.scanning = True
        self.window["-SCAN-"].update(disabled=True)
        self.window["-STOP-"].update(disabled=False)
        self.window["-STATUS-"].update("Scanning market for opportunities...")
        
        # Start scanning thread
        scan_thread = threading.Thread(target=self.scan_worker, daemon=True)
        scan_thread.start()
    
    def scan_worker(self):
        """Simulate the scanning process."""
        steps = [
            "Loading S&P 500 universe...",
            "Filtering by market cap and volume...",
            "Analyzing TTM Squeeze conditions...",
            "Checking momentum indicators...",
            "Evaluating RSI levels...",
            "Finding option contracts...",
            "Calculating position sizes...",
            "Executing trades..."
        ]
        
        for i, step in enumerate(steps):
            if not self.scanning:
                break
                
            self.log_message(step)
            self.window["-STATUS-"].update(f"Step {i+1}/8: {step}")
            time.sleep(1)
        
        if self.scanning:
            # Simulate finding signals
            self.log_message("Found 2 high-probability signals!")
            self.log_message("Executed trade: AAPL Call $180 - Bullish breakout")
            self.log_message("Executed trade: TSLA Put $240 - Bearish breakout")
            
            # Update stats
            self.trades_count += 2
            self.pnl += 290.00
            self.last_scan = datetime.now().strftime("%H:%M:%S")
            
            self.window["-TRADES-COUNT-"].update(str(self.trades_count))
            self.window["-PNL-"].update(f"${self.pnl:.2f}")
            self.window["-LAST-SCAN-"].update(self.last_scan)
            self.window["-STATUS-"].update("Scan completed - 2 trades executed!")
        
        self.scanning = False
        self.window["-SCAN-"].update(disabled=False)
        self.window["-STOP-"].update(disabled=True)
    
    def stop_scan(self):
        """Stop the scanning process."""
        self.scanning = False
        self.window["-STATUS-"].update("Scan stopped by user")
        self.log_message("Scan stopped by user")
    
    def show_settings(self):
        """Show settings dialog."""
        settings_layout = [
            [sg.Text("Trading Settings", font=("Arial", 14, "bold"))],
            [sg.Text("This is a demo - settings would be configurable in the full version")],
            [sg.HSeparator()],
            [sg.Text("Max Trades per Day:"), sg.Input("3", disabled=True, size=(10, 1))],
            [sg.Text("Risk per Trade (%):"), sg.Input("2.0", disabled=True, size=(10, 1))],
            [sg.Text("Stop Loss (%):"), sg.Input("50", disabled=True, size=(10, 1))],
            [sg.Text("Take Profit (%):"), sg.Input("100", disabled=True, size=(10, 1))],
            [sg.Checkbox("Paper Trading", default=True, disabled=True)],
            [sg.Button("Close", key="-CLOSE-SETTINGS-")]
        ]
        
        settings_window = sg.Window("Settings - Demo", settings_layout, modal=True)
        
        while True:
            event, values = settings_window.read()
            if event in (sg.WIN_CLOSED, "-CLOSE-SETTINGS-"):
                break
        
        settings_window.close()
    
    def handle_signal_selection(self, values):
        """Handle signal table selection."""
        try:
            if values["-SIGNALS-TABLE-"]:
                row = values["-SIGNALS-TABLE-"][0]
                if row < len(self.signals):
                    signal = self.signals[row]
                    self.show_signal_details(signal)
        except Exception as e:
            print(f"Error handling signal selection: {e}")
    
    def show_signal_details(self, signal):
        """Show detailed information about a signal."""
        symbol, signal_type, strength, price, notes = signal
        
        details = f"""
Signal Details for {symbol}

Signal Type: {signal_type}
Strength: {strength}
Current Price: {price}
Notes: {notes}

In the full version, this would show:
• Complete technical analysis
• Option contract recommendations
• Risk/reward calculations
• Entry and exit strategies
        """
        
        sg.popup_scrolled(details, title=f"Signal Details - {symbol}", size=(50, 15))
    
    def log_message(self, message):
        """Add a message to the log."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        current_log = self.window["-LOG-"].get()
        self.window["-LOG-"].update(current_log + log_entry)
    
    def run(self):
        """Run the GUI demo."""
        layout = self.create_layout()
        
        self.window = sg.Window(
            "AutoPilot Options Trader - Demo",
            layout,
            size=(1200, 800),
            resizable=True,
            finalize=True
        )
        
        self.log_message("GUI Demo started - This shows what the real interface looks like!")
        self.log_message("Click 'Scan & Trade' to see the AI in action")
        
        # Main event loop
        while True:
            event, values = self.window.read(timeout=100)
            
            if event == sg.WIN_CLOSED:
                break
            elif event == "-SCAN-":
                self.start_scan_demo()
            elif event == "-STOP-":
                self.stop_scan()
            elif event == "-SETTINGS-":
                self.show_settings()
            elif event == "-SIGNALS-TABLE-":
                self.handle_signal_selection(values)
            
            # Simulate live updates
            if not self.scanning and random.random() < 0.01:  # 1% chance per cycle
                # Simulate position updates
                for i, pos in enumerate(self.positions):
                    if pos[6] == "Open":  # If position is open
                        # Simulate price movement
                        current_price = float(pos[4].replace('$', ''))
                        change = random.uniform(-0.10, 0.10)
                        new_price = current_price + change
                        
                        entry_price = float(pos[3].replace('$', ''))
                        qty = int(pos[2])
                        pnl = (new_price - entry_price) * qty * 100
                        
                        self.positions[i][4] = f"${new_price:.2f}"
                        self.positions[i][5] = f"${pnl:+.0f}"
                
                # Update positions table
                self.window["-POSITIONS-TABLE-"].update(values=self.positions)
        
        self.window.close()

def main():
    """Main function to run the GUI demo."""
    print("🖥️  Starting AutoPilot Options Trader GUI Demo...")
    print("   A desktop window will open showing the trading interface")
    print("   Click 'Scan & Trade' to see the AI in action!")
    print()
    
    try:
        demo = TradingGUIDemo()
        demo.run()
        
        print("✅ GUI Demo completed!")
        print("   This is what the real trading interface looks like")
        print("   In the full version, it would execute real trades")
        
    except Exception as e:
        print(f"❌ GUI Demo error: {e}")
        print("   Make sure PySimpleGUI is properly installed")

if __name__ == "__main__":
    main()
