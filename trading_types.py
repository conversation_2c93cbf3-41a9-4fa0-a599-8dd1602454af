"""
Trading Types and Data Structures
Common types used across the advanced options trading system
"""

from dataclasses import dataclass
from enum import Enum
from typing import Dict, List, Optional
from datetime import datetime

class MarketRegime(Enum):
    """Market regime classifications."""
    TRENDING_UP = "trending_up"
    TRENDING_DOWN = "trending_down"
    RANGING = "ranging"
    HIGH_VOLATILITY = "high_volatility"
    LOW_VOLATILITY = "low_volatility"
    PRE_EARNINGS = "pre_earnings"
    POST_EARNINGS = "post_earnings"

class StrategyType(Enum):
    """Available options strategies."""
    # Directional Strategies
    LONG_CALL = "long_call"
    LONG_PUT = "long_put"
    BULL_CALL_SPREAD = "bull_call_spread"
    BEAR_PUT_SPREAD = "bear_put_spread"
    
    # Volatility Strategies
    LONG_STRADDLE = "long_straddle"
    LONG_STRANGLE = "long_strangle"
    SHORT_STRADDLE = "short_straddle"
    SHORT_STRANGLE = "short_strangle"
    
    # Income Strategies
    COVERED_CALL = "covered_call"
    CASH_SECURED_PUT = "cash_secured_put"
    IRON_CONDOR = "iron_condor"
    IRON_BUTTERFLY = "iron_butterfly"
    
    # Advanced Strategies
    BUTTERFLY_SPREAD = "butterfly_spread"
    CALENDAR_SPREAD = "calendar_spread"
    DIAGONAL_SPREAD = "diagonal_spread"
    RATIO_SPREAD = "ratio_spread"

@dataclass
class Greeks:
    """Option Greeks container."""
    delta: float
    gamma: float
    theta: float
    vega: float
    rho: float

@dataclass
class VolatilityProfile:
    """Volatility analysis for a stock."""
    implied_vol: float
    historical_vol: float
    iv_rank: float  # 0-100 percentile
    iv_percentile: float  # 0-100 percentile
    vol_skew: float
    term_structure: Dict[int, float]  # DTE -> IV

@dataclass
class MarketConditions:
    """Current market conditions for a symbol."""
    regime: MarketRegime
    volatility_profile: VolatilityProfile
    price_trend: float  # -1 to 1
    support_resistance: tuple
    earnings_date: Optional[datetime]
    days_to_earnings: Optional[int]
    volume_profile: Dict[str, float]

@dataclass
class StrategyScore:
    """Strategy scoring and ranking."""
    strategy: StrategyType
    score: float
    confidence: float
    expected_return: float
    max_risk: float
    win_probability: float
    reasoning: str

@dataclass
class Position:
    """Multi-leg options position."""
    symbol: str
    strategy: StrategyType
    legs: List[Dict]
    entry_time: datetime
    total_cost: float
    max_profit: float
    max_loss: float
    breakeven_points: List[float]
    greeks: Greeks
    target_profit: float
    stop_loss: float
    days_to_expiry: int
    status: str = "open"
