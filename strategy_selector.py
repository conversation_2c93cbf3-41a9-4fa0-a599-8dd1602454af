"""
Advanced Strategy Selector for Options Trading
Dynamically selects optimal strategies based on market conditions
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple
import logging
from datetime import datetime, timedelta

from trading_types import (
    MarketRegime, StrategyType, StrategyScore, MarketConditions, VolatilityProfile
)

class StrategySelector:
    """Intelligent strategy selection based on market conditions."""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Strategy mappings for different market conditions
        self.strategy_matrix = self._build_strategy_matrix()
        
        # Historical performance weights (would be loaded from database)
        self.performance_weights = self._initialize_performance_weights()
    
    async def rank_strategies(self, symbol: str, market_conditions: MarketConditions) -> List[StrategyScore]:
        """Rank all strategies for given market conditions."""
        strategy_scores = []
        
        # Get applicable strategies for current regime
        applicable_strategies = self._get_applicable_strategies(market_conditions)
        
        for strategy in applicable_strategies:
            score = await self._score_strategy(strategy, symbol, market_conditions)
            if score.score > 0.3:  # Minimum threshold
                strategy_scores.append(score)
        
        # Sort by score (highest first)
        strategy_scores.sort(key=lambda x: x.score, reverse=True)
        
        self.logger.info(f"Ranked {len(strategy_scores)} strategies for {symbol}")
        return strategy_scores
    
    def _build_strategy_matrix(self) -> Dict[MarketRegime, List[StrategyType]]:
        """Build strategy matrix for different market regimes."""
        return {
            MarketRegime.TRENDING_UP: [
                StrategyType.LONG_CALL,
                StrategyType.BULL_CALL_SPREAD,
                StrategyType.COVERED_CALL,
                StrategyType.CASH_SECURED_PUT
            ],
            
            MarketRegime.TRENDING_DOWN: [
                StrategyType.LONG_PUT,
                StrategyType.BEAR_PUT_SPREAD,
                StrategyType.LONG_STRADDLE,
                StrategyType.LONG_STRANGLE
            ],
            
            MarketRegime.RANGING: [
                StrategyType.IRON_CONDOR,
                StrategyType.IRON_BUTTERFLY,
                StrategyType.SHORT_STRADDLE,
                StrategyType.SHORT_STRANGLE,
                StrategyType.BUTTERFLY_SPREAD
            ],
            
            MarketRegime.HIGH_VOLATILITY: [
                StrategyType.IRON_CONDOR,
                StrategyType.SHORT_STRADDLE,
                StrategyType.SHORT_STRANGLE,
                StrategyType.CALENDAR_SPREAD
            ],
            
            MarketRegime.LOW_VOLATILITY: [
                StrategyType.LONG_STRADDLE,
                StrategyType.LONG_STRANGLE,
                StrategyType.CALENDAR_SPREAD,
                StrategyType.DIAGONAL_SPREAD
            ],
            
            MarketRegime.PRE_EARNINGS: [
                StrategyType.LONG_STRADDLE,
                StrategyType.LONG_STRANGLE,
                StrategyType.IRON_CONDOR
            ],
            
            MarketRegime.POST_EARNINGS: [
                StrategyType.COVERED_CALL,
                StrategyType.CASH_SECURED_PUT,
                StrategyType.IRON_BUTTERFLY
            ]
        }
    
    def _get_applicable_strategies(self, market_conditions: MarketConditions) -> List[StrategyType]:
        """Get strategies applicable to current market conditions."""
        base_strategies = self.strategy_matrix.get(market_conditions.regime, [])
        
        # Add volatility-based strategies
        vol_profile = market_conditions.volatility_profile
        
        if vol_profile.iv_rank > 75:  # High IV
            base_strategies.extend([
                StrategyType.IRON_CONDOR,
                StrategyType.SHORT_STRADDLE,
                StrategyType.COVERED_CALL
            ])
        elif vol_profile.iv_rank < 25:  # Low IV
            base_strategies.extend([
                StrategyType.LONG_STRADDLE,
                StrategyType.LONG_STRANGLE,
                StrategyType.CALENDAR_SPREAD
            ])
        
        # Remove duplicates
        return list(set(base_strategies))
    
    async def _score_strategy(self, strategy: StrategyType, symbol: str, 
                            market_conditions: MarketConditions) -> StrategyScore:
        """Score a strategy based on market conditions and historical performance."""
        
        # Base scoring components
        regime_score = self._score_regime_fit(strategy, market_conditions.regime)
        volatility_score = self._score_volatility_fit(strategy, market_conditions.volatility_profile)
        trend_score = self._score_trend_fit(strategy, market_conditions.price_trend)
        earnings_score = self._score_earnings_fit(strategy, market_conditions.days_to_earnings)
        
        # Historical performance weight
        performance_weight = self.performance_weights.get(strategy, 1.0)
        
        # Calculate composite score
        base_score = (
            regime_score * 0.3 +
            volatility_score * 0.3 +
            trend_score * 0.2 +
            earnings_score * 0.2
        )
        
        final_score = base_score * performance_weight
        
        # Calculate expected metrics
        expected_return, max_risk, win_prob = self._calculate_strategy_metrics(
            strategy, market_conditions
        )
        
        # Generate reasoning
        reasoning = self._generate_reasoning(
            strategy, market_conditions, regime_score, volatility_score
        )
        
        return StrategyScore(
            strategy=strategy,
            score=final_score,
            confidence=min(0.95, base_score + 0.1),
            expected_return=expected_return,
            max_risk=max_risk,
            win_probability=win_prob,
            reasoning=reasoning
        )
    
    def _score_regime_fit(self, strategy: StrategyType, regime: MarketRegime) -> float:
        """Score how well strategy fits market regime."""
        regime_strategies = self.strategy_matrix.get(regime, [])
        
        if strategy in regime_strategies:
            # Primary strategies for regime
            return 1.0
        
        # Check secondary fits
        secondary_fits = {
            (StrategyType.LONG_CALL, MarketRegime.LOW_VOLATILITY): 0.7,
            (StrategyType.LONG_PUT, MarketRegime.HIGH_VOLATILITY): 0.7,
            (StrategyType.IRON_CONDOR, MarketRegime.TRENDING_UP): 0.5,
            (StrategyType.COVERED_CALL, MarketRegime.RANGING): 0.8,
        }
        
        return secondary_fits.get((strategy, regime), 0.3)
    
    def _score_volatility_fit(self, strategy: StrategyType, vol_profile: VolatilityProfile) -> float:
        """Score strategy fit based on volatility conditions."""
        iv_rank = vol_profile.iv_rank
        
        # Volatility selling strategies (benefit from high IV)
        vol_selling = [
            StrategyType.IRON_CONDOR, StrategyType.SHORT_STRADDLE,
            StrategyType.SHORT_STRANGLE, StrategyType.COVERED_CALL
        ]
        
        # Volatility buying strategies (benefit from low IV)
        vol_buying = [
            StrategyType.LONG_STRADDLE, StrategyType.LONG_STRANGLE,
            StrategyType.LONG_CALL, StrategyType.LONG_PUT
        ]
        
        if strategy in vol_selling:
            # Higher score for higher IV
            return min(1.0, iv_rank / 75)
        elif strategy in vol_buying:
            # Higher score for lower IV
            return min(1.0, (100 - iv_rank) / 75)
        else:
            # Neutral strategies
            return 0.7
    
    def _score_trend_fit(self, strategy: StrategyType, price_trend: float) -> float:
        """Score strategy fit based on price trend."""
        
        # Bullish strategies
        bullish_strategies = [
            StrategyType.LONG_CALL, StrategyType.BULL_CALL_SPREAD,
            StrategyType.COVERED_CALL, StrategyType.CASH_SECURED_PUT
        ]
        
        # Bearish strategies
        bearish_strategies = [
            StrategyType.LONG_PUT, StrategyType.BEAR_PUT_SPREAD
        ]
        
        # Neutral strategies
        neutral_strategies = [
            StrategyType.IRON_CONDOR, StrategyType.IRON_BUTTERFLY,
            StrategyType.SHORT_STRADDLE, StrategyType.BUTTERFLY_SPREAD
        ]
        
        if strategy in bullish_strategies:
            return max(0.2, (price_trend + 1) / 2)  # 0.2 to 1.0
        elif strategy in bearish_strategies:
            return max(0.2, (1 - price_trend) / 2)  # 0.2 to 1.0
        elif strategy in neutral_strategies:
            return max(0.5, 1 - abs(price_trend))  # Higher score for low trend
        else:
            return 0.6  # Default
    
    def _score_earnings_fit(self, strategy: StrategyType, days_to_earnings: Optional[int]) -> float:
        """Score strategy fit based on earnings proximity."""
        if days_to_earnings is None:
            return 0.8  # Neutral if no earnings data
        
        # Pre-earnings strategies (high volatility expected)
        pre_earnings = [
            StrategyType.LONG_STRADDLE, StrategyType.LONG_STRANGLE,
            StrategyType.IRON_CONDOR
        ]
        
        # Post-earnings strategies (volatility crush expected)
        post_earnings = [
            StrategyType.COVERED_CALL, StrategyType.CASH_SECURED_PUT,
            StrategyType.IRON_BUTTERFLY
        ]
        
        if 0 <= days_to_earnings <= 7:  # Pre-earnings
            if strategy in pre_earnings:
                return 1.0
            elif strategy in post_earnings:
                return 0.3
        elif -7 <= days_to_earnings < 0:  # Post-earnings
            if strategy in post_earnings:
                return 1.0
            elif strategy in pre_earnings:
                return 0.3
        
        return 0.7  # Neutral for other periods
    
    def _calculate_strategy_metrics(self, strategy: StrategyType, 
                                  market_conditions: MarketConditions) -> Tuple[float, float, float]:
        """Calculate expected return, max risk, and win probability."""
        
        # Simplified metrics - would use more sophisticated models in production
        base_metrics = {
            StrategyType.LONG_CALL: (0.15, 1.0, 0.45),
            StrategyType.LONG_PUT: (0.15, 1.0, 0.45),
            StrategyType.BULL_CALL_SPREAD: (0.25, 0.75, 0.55),
            StrategyType.BEAR_PUT_SPREAD: (0.25, 0.75, 0.55),
            StrategyType.IRON_CONDOR: (0.10, 0.30, 0.70),
            StrategyType.LONG_STRADDLE: (0.30, 1.0, 0.40),
            StrategyType.SHORT_STRADDLE: (0.15, 3.0, 0.65),
            StrategyType.COVERED_CALL: (0.08, 0.20, 0.75),
            StrategyType.CASH_SECURED_PUT: (0.08, 0.20, 0.75),
        }
        
        expected_return, max_risk, win_prob = base_metrics.get(strategy, (0.10, 0.50, 0.50))
        
        # Adjust based on market conditions
        iv_rank = market_conditions.volatility_profile.iv_rank
        
        # High IV generally increases potential returns but also risk
        if iv_rank > 75:
            expected_return *= 1.2
            max_risk *= 1.1
        elif iv_rank < 25:
            expected_return *= 0.8
            max_risk *= 0.9
        
        return expected_return, max_risk, win_prob
    
    def _generate_reasoning(self, strategy: StrategyType, market_conditions: MarketConditions,
                          regime_score: float, volatility_score: float) -> str:
        """Generate human-readable reasoning for strategy selection."""
        
        regime = market_conditions.regime.value.replace('_', ' ').title()
        iv_rank = market_conditions.volatility_profile.iv_rank
        
        reasoning_parts = [
            f"Market regime: {regime}",
            f"IV Rank: {iv_rank:.0f}%"
        ]
        
        if regime_score > 0.8:
            reasoning_parts.append("Strong regime fit")
        elif regime_score > 0.6:
            reasoning_parts.append("Good regime fit")
        
        if volatility_score > 0.8:
            if iv_rank > 75:
                reasoning_parts.append("High IV favors vol selling")
            else:
                reasoning_parts.append("Low IV favors vol buying")
        
        if market_conditions.days_to_earnings and market_conditions.days_to_earnings <= 7:
            reasoning_parts.append("Earnings event approaching")
        
        return "; ".join(reasoning_parts)
    
    def _initialize_performance_weights(self) -> Dict[StrategyType, float]:
        """Initialize performance weights for strategies."""
        # In production, these would be loaded from historical performance data
        return {
            StrategyType.IRON_CONDOR: 1.1,
            StrategyType.COVERED_CALL: 1.05,
            StrategyType.CASH_SECURED_PUT: 1.05,
            StrategyType.BULL_CALL_SPREAD: 0.95,
            StrategyType.LONG_STRADDLE: 0.9,
            # ... other strategies default to 1.0
        }
    
    def update_performance_weight(self, strategy: StrategyType, performance_factor: float):
        """Update performance weight based on recent results."""
        current_weight = self.performance_weights.get(strategy, 1.0)
        # Exponential moving average update
        alpha = 0.1  # Learning rate
        new_weight = current_weight * (1 - alpha) + performance_factor * alpha
        self.performance_weights[strategy] = max(0.5, min(2.0, new_weight))  # Clamp weights
