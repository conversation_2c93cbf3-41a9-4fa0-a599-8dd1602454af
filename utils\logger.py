"""
Logging configuration for AutoPilot Options Trader
"""

import logging
import logging.handlers
import os
from pathlib import Path
import colorlog

def setup_logging(log_level: str = "INFO", log_file: str = "logs/trading.log"):
    """Setup logging configuration with both file and console handlers."""
    
    # Create logs directory if it doesn't exist
    log_path = Path(log_file)
    log_path.parent.mkdir(parents=True, exist_ok=True)
    
    # Configure root logger
    root_logger = logging.getLogger()
    root_logger.setLevel(getattr(logging, log_level.upper()))
    
    # Clear any existing handlers
    root_logger.handlers.clear()
    
    # Create formatters
    file_formatter = logging.Formatter(
        '%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%Y-%m-%d %H:%M:%S'
    )
    
    console_formatter = colorlog.ColoredFormatter(
        '%(log_color)s%(asctime)s - %(name)s - %(levelname)s - %(message)s',
        datefmt='%H:%M:%S',
        log_colors={
            'DEBUG': 'cyan',
            'INFO': 'green',
            'WARNING': 'yellow',
            'ERROR': 'red',
            'CRITICAL': 'red,bg_white',
        }
    )
    
    # File handler with rotation
    file_handler = logging.handlers.RotatingFileHandler(
        log_file,
        maxBytes=10*1024*1024,  # 10MB
        backupCount=5,
        encoding='utf-8'
    )
    file_handler.setLevel(logging.DEBUG)
    file_handler.setFormatter(file_formatter)
    
    # Console handler
    console_handler = colorlog.StreamHandler()
    console_handler.setLevel(getattr(logging, log_level.upper()))
    console_handler.setFormatter(console_formatter)
    
    # Add handlers to root logger
    root_logger.addHandler(file_handler)
    root_logger.addHandler(console_handler)
    
    # Set specific logger levels
    logging.getLogger('urllib3').setLevel(logging.WARNING)
    logging.getLogger('requests').setLevel(logging.WARNING)
    logging.getLogger('alpaca_trade_api').setLevel(logging.INFO)
    
    return root_logger

def get_trade_logger():
    """Get a specific logger for trade events."""
    trade_logger = logging.getLogger('trades')
    
    # Create trade-specific file handler if not exists
    if not any(isinstance(h, logging.FileHandler) and 'trade_log.csv' in h.baseFilename 
               for h in trade_logger.handlers):
        
        trade_handler = logging.FileHandler('logs/trade_log.csv', encoding='utf-8')
        trade_formatter = logging.Formatter('%(message)s')  # CSV format
        trade_handler.setFormatter(trade_formatter)
        trade_logger.addHandler(trade_handler)
        trade_logger.setLevel(logging.INFO)
        
        # Write CSV header if file is new
        if os.path.getsize('logs/trade_log.csv') == 0:
            trade_logger.info('timestamp,symbol,action,option_type,strike,expiry,quantity,premium,total_cost,stop_loss,take_profit,signal_strength,notes')
    
    return trade_logger
