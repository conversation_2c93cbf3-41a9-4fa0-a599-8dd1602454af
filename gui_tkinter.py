#!/usr/bin/env python3
"""
Tkinter GUI for AutoPilot Options Trader
Free desktop interface using built-in Python Tkinter
"""

import tkinter as tk
from tkinter import ttk, messagebox, scrolledtext
import threading
import time
import random
from datetime import datetime

class TradingGUI:
    """Main GUI application using Tkinter."""
    
    def __init__(self):
        self.root = tk.Tk()
        self.root.title("🤖 AutoPilot Options Trader")
        self.root.geometry("1200x800")
        self.root.configure(bg='#2b2b2b')
        
        # Configure style
        self.style = ttk.Style()
        self.style.theme_use('clam')
        self.configure_styles()
        
        # Demo data
        self.scanning = False
        self.trades_count = 3
        self.pnl = 200.00
        self.last_scan = "Never"
        
        # Create GUI elements
        self.create_widgets()
        self.create_sample_data()
        
    def configure_styles(self):
        """Configure custom styles for the GUI."""
        # Configure colors for dark theme
        self.style.configure('Title.TLabel', 
                           font=('Arial', 20, 'bold'),
                           background='#2b2b2b',
                           foreground='white')
        
        self.style.configure('Heading.TLabel',
                           font=('Arial', 14, 'bold'),
                           background='#2b2b2b',
                           foreground='white')
        
        self.style.configure('Info.TLabel',
                           font=('Arial', 10),
                           background='#2b2b2b',
                           foreground='lightgray')
        
        self.style.configure('Scan.TButton',
                           font=('Arial', 12, 'bold'))
        
    def create_widgets(self):
        """Create all GUI widgets."""
        
        # Main container
        main_frame = ttk.Frame(self.root)
        main_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Header
        header_frame = ttk.Frame(main_frame)
        header_frame.pack(fill=tk.X, pady=(0, 10))
        
        title_label = ttk.Label(header_frame, text="🤖 AutoPilot Options Trader", style='Title.TLabel')
        title_label.pack()
        
        subtitle_label = ttk.Label(header_frame, text="AI-Powered Options Trading for Beginners", style='Info.TLabel')
        subtitle_label.pack()
        
        # Control panel
        control_frame = ttk.LabelFrame(main_frame, text="Trading Controls", padding=10)
        control_frame.pack(fill=tk.X, pady=(0, 10))
        
        button_frame = ttk.Frame(control_frame)
        button_frame.pack(fill=tk.X)
        
        self.scan_button = ttk.Button(button_frame, text="🔍 Scan & Trade", 
                                     command=self.start_scan, style='Scan.TButton')
        self.scan_button.pack(side=tk.LEFT, padx=(0, 10))
        
        self.stop_button = ttk.Button(button_frame, text="⏹️ Stop", 
                                     command=self.stop_scan, state=tk.DISABLED)
        self.stop_button.pack(side=tk.LEFT, padx=(0, 10))
        
        settings_button = ttk.Button(button_frame, text="⚙️ Settings", 
                                   command=self.show_settings)
        settings_button.pack(side=tk.LEFT)
        
        self.status_label = ttk.Label(control_frame, text="Ready to scan for opportunities", 
                                     style='Info.TLabel')
        self.status_label.pack(anchor=tk.W, pady=(10, 0))
        
        # Statistics panel
        stats_frame = ttk.LabelFrame(main_frame, text="Today's Statistics", padding=10)
        stats_frame.pack(fill=tk.X, pady=(0, 10))
        
        stats_grid = ttk.Frame(stats_frame)
        stats_grid.pack(fill=tk.X)
        
        # Stats row 1
        ttk.Label(stats_grid, text="Trades:", style='Info.TLabel').grid(row=0, column=0, sticky=tk.W)
        self.trades_label = ttk.Label(stats_grid, text=str(self.trades_count), style='Info.TLabel')
        self.trades_label.grid(row=0, column=1, sticky=tk.W, padx=(10, 30))
        
        ttk.Label(stats_grid, text="P&L:", style='Info.TLabel').grid(row=0, column=2, sticky=tk.W)
        self.pnl_label = ttk.Label(stats_grid, text=f"${self.pnl:.2f}", style='Info.TLabel')
        self.pnl_label.grid(row=0, column=3, sticky=tk.W, padx=(10, 0))
        
        # Stats row 2
        ttk.Label(stats_grid, text="Win Rate:", style='Info.TLabel').grid(row=1, column=0, sticky=tk.W)
        self.winrate_label = ttk.Label(stats_grid, text="67%", style='Info.TLabel')
        self.winrate_label.grid(row=1, column=1, sticky=tk.W, padx=(10, 30))
        
        ttk.Label(stats_grid, text="Last Scan:", style='Info.TLabel').grid(row=1, column=2, sticky=tk.W)
        self.lastscan_label = ttk.Label(stats_grid, text=self.last_scan, style='Info.TLabel')
        self.lastscan_label.grid(row=1, column=3, sticky=tk.W, padx=(10, 0))
        
        # Content area with notebook
        content_frame = ttk.Frame(main_frame)
        content_frame.pack(fill=tk.BOTH, expand=True)
        
        # Create notebook for tabs
        self.notebook = ttk.Notebook(content_frame)
        self.notebook.pack(fill=tk.BOTH, expand=True)
        
        # Signals tab
        signals_frame = ttk.Frame(self.notebook)
        self.notebook.add(signals_frame, text="🎯 Signals")
        
        # Signals table
        signals_table_frame = ttk.Frame(signals_frame)
        signals_table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        # Create treeview for signals
        self.signals_tree = ttk.Treeview(signals_table_frame, 
                                        columns=('Signal', 'Strength', 'Price', 'Notes'),
                                        show='tree headings')
        
        self.signals_tree.heading('#0', text='Symbol')
        self.signals_tree.heading('Signal', text='Signal')
        self.signals_tree.heading('Strength', text='Strength')
        self.signals_tree.heading('Price', text='Price')
        self.signals_tree.heading('Notes', text='Notes')
        
        self.signals_tree.column('#0', width=80)
        self.signals_tree.column('Signal', width=120)
        self.signals_tree.column('Strength', width=80)
        self.signals_tree.column('Price', width=80)
        self.signals_tree.column('Notes', width=300)
        
        # Add scrollbar to signals table
        signals_scrollbar = ttk.Scrollbar(signals_table_frame, orient=tk.VERTICAL, 
                                         command=self.signals_tree.yview)
        self.signals_tree.configure(yscrollcommand=signals_scrollbar.set)
        
        self.signals_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        signals_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Positions tab
        positions_frame = ttk.Frame(self.notebook)
        self.notebook.add(positions_frame, text="📊 Positions")
        
        # Positions table
        positions_table_frame = ttk.Frame(positions_frame)
        positions_table_frame.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.positions_tree = ttk.Treeview(positions_table_frame,
                                          columns=('Type', 'Qty', 'Entry', 'Current', 'P&L', 'Status'),
                                          show='tree headings')
        
        self.positions_tree.heading('#0', text='Symbol')
        self.positions_tree.heading('Type', text='Type')
        self.positions_tree.heading('Qty', text='Qty')
        self.positions_tree.heading('Entry', text='Entry')
        self.positions_tree.heading('Current', text='Current')
        self.positions_tree.heading('P&L', text='P&L')
        self.positions_tree.heading('Status', text='Status')
        
        for col in ['#0', 'Type', 'Qty', 'Entry', 'Current', 'P&L', 'Status']:
            self.positions_tree.column(col, width=100)
        
        positions_scrollbar = ttk.Scrollbar(positions_table_frame, orient=tk.VERTICAL,
                                           command=self.positions_tree.yview)
        self.positions_tree.configure(yscrollcommand=positions_scrollbar.set)
        
        self.positions_tree.pack(side=tk.LEFT, fill=tk.BOTH, expand=True)
        positions_scrollbar.pack(side=tk.RIGHT, fill=tk.Y)
        
        # Log tab
        log_frame = ttk.Frame(self.notebook)
        self.notebook.add(log_frame, text="📝 Activity Log")
        
        # Activity log
        log_container = ttk.Frame(log_frame)
        log_container.pack(fill=tk.BOTH, expand=True, padx=10, pady=10)
        
        self.log_text = scrolledtext.ScrolledText(log_container, 
                                                 font=('Courier', 9),
                                                 bg='#1e1e1e',
                                                 fg='lightgreen',
                                                 insertbackground='white')
        self.log_text.pack(fill=tk.BOTH, expand=True)
        
        # Initial log message
        self.log_message("AutoPilot Options Trader initialized...")
        self.log_message("[Demo Mode] - This is a demonstration of the GUI interface")
        self.log_message("Click 'Scan & Trade' to see the AI in action!")
        
    def create_sample_data(self):
        """Create sample data for the demo."""
        # Sample signals
        signals_data = [
            ("AAPL", "Bullish Breakout", "0.85", "$175.50", "TTM Squeeze ending; Strong momentum"),
            ("TSLA", "Bearish Breakout", "0.72", "$245.30", "Volume surge; RSI overbought"),
            ("MSFT", "Squeeze Building", "0.65", "$380.20", "Bollinger bands contracting"),
            ("GOOGL", "Bullish Breakout", "0.78", "$142.80", "Breaking above resistance"),
            ("NVDA", "Bearish Breakout", "0.69", "$875.40", "Momentum turning negative")
        ]
        
        # Populate signals table
        for symbol, signal, strength, price, notes in signals_data:
            self.signals_tree.insert('', tk.END, text=symbol, 
                                   values=(signal, strength, price, notes))
        
        # Sample positions
        positions_data = [
            ("AAPL", "Call $180", "2", "$3.50", "$4.20", "+$140", "Open"),
            ("TSLA", "Put $240", "1", "$5.80", "$7.30", "+$150", "Open"),
            ("MSFT", "Call $385", "3", "$2.10", "$1.80", "-$90", "Open")
        ]
        
        # Populate positions table
        for symbol, type_str, qty, entry, current, pnl, status in positions_data:
            self.positions_tree.insert('', tk.END, text=symbol,
                                     values=(type_str, qty, entry, current, pnl, status))
    
    def start_scan(self):
        """Start the scanning process."""
        if self.scanning:
            return
            
        self.scanning = True
        self.scan_button.configure(state=tk.DISABLED)
        self.stop_button.configure(state=tk.NORMAL)
        self.status_label.configure(text="Scanning market for opportunities...")
        
        # Start scanning in background thread
        scan_thread = threading.Thread(target=self.scan_worker, daemon=True)
        scan_thread.start()
    
    def scan_worker(self):
        """Simulate the scanning process."""
        steps = [
            "Loading S&P 500 universe...",
            "Filtering by market cap and volume...",
            "Analyzing TTM Squeeze conditions...",
            "Checking momentum indicators...",
            "Evaluating RSI levels...",
            "Finding option contracts...",
            "Calculating position sizes...",
            "Executing trades..."
        ]
        
        for i, step in enumerate(steps):
            if not self.scanning:
                break
                
            self.log_message(step)
            self.root.after(0, lambda s=f"Step {i+1}/8: {step}": self.status_label.configure(text=s))
            time.sleep(1)
        
        if self.scanning:
            # Simulate successful scan
            self.log_message("Found 2 high-probability signals!")
            self.log_message("Executed trade: AAPL Call $180 - Bullish breakout")
            self.log_message("Executed trade: TSLA Put $240 - Bearish breakout")
            
            # Update stats
            self.trades_count += 2
            self.pnl += 290.00
            self.last_scan = datetime.now().strftime("%H:%M:%S")
            
            # Update GUI on main thread
            self.root.after(0, self.update_stats)
            self.root.after(0, lambda: self.status_label.configure(text="Scan completed - 2 trades executed!"))
        
        # Re-enable buttons
        self.scanning = False
        self.root.after(0, lambda: self.scan_button.configure(state=tk.NORMAL))
        self.root.after(0, lambda: self.stop_button.configure(state=tk.DISABLED))
    
    def stop_scan(self):
        """Stop the scanning process."""
        self.scanning = False
        self.status_label.configure(text="Scan stopped by user")
        self.log_message("Scan stopped by user")
    
    def update_stats(self):
        """Update statistics display."""
        self.trades_label.configure(text=str(self.trades_count))
        self.pnl_label.configure(text=f"${self.pnl:.2f}")
        self.lastscan_label.configure(text=self.last_scan)
    
    def show_settings(self):
        """Show settings dialog."""
        settings_window = tk.Toplevel(self.root)
        settings_window.title("Settings - Demo")
        settings_window.geometry("400x300")
        settings_window.configure(bg='#2b2b2b')
        
        # Make it modal
        settings_window.transient(self.root)
        settings_window.grab_set()
        
        # Settings content
        ttk.Label(settings_window, text="Trading Settings", style='Heading.TLabel').pack(pady=10)
        ttk.Label(settings_window, text="This is a demo - settings would be configurable in the full version", 
                 style='Info.TLabel').pack(pady=10)
        
        # Settings form
        form_frame = ttk.Frame(settings_window)
        form_frame.pack(fill=tk.BOTH, expand=True, padx=20, pady=10)
        
        settings_data = [
            ("Max Trades per Day:", "3"),
            ("Risk per Trade (%):", "2.0"),
            ("Stop Loss (%):", "50"),
            ("Take Profit (%):", "100")
        ]
        
        for i, (label, value) in enumerate(settings_data):
            ttk.Label(form_frame, text=label, style='Info.TLabel').grid(row=i, column=0, sticky=tk.W, pady=5)
            entry = ttk.Entry(form_frame, state=tk.DISABLED)
            entry.insert(0, value)
            entry.grid(row=i, column=1, sticky=tk.W, padx=(10, 0), pady=5)
        
        # Paper trading checkbox
        paper_var = tk.BooleanVar(value=True)
        ttk.Checkbutton(form_frame, text="Paper Trading", variable=paper_var, 
                       state=tk.DISABLED).grid(row=len(settings_data), column=0, columnspan=2, sticky=tk.W, pady=10)
        
        # Close button
        ttk.Button(settings_window, text="Close", 
                  command=settings_window.destroy).pack(pady=10)
    
    def log_message(self, message):
        """Add a message to the activity log."""
        timestamp = datetime.now().strftime("%H:%M:%S")
        log_entry = f"[{timestamp}] {message}\n"
        
        # Update log on main thread
        self.root.after(0, lambda: self.log_text.insert(tk.END, log_entry))
        self.root.after(0, lambda: self.log_text.see(tk.END))
    
    def run(self):
        """Run the GUI application."""
        self.root.mainloop()

def main():
    """Main function to run the GUI."""
    print("🖥️  Starting AutoPilot Options Trader GUI...")
    print("   Opening desktop window with Tkinter (free!)")
    print("   Click 'Scan & Trade' to see the AI in action!")
    
    try:
        app = TradingGUI()
        app.run()
        
        print("✅ GUI application closed")
        
    except Exception as e:
        print(f"❌ GUI error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
