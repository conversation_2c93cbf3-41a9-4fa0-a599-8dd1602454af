"""
Configuration management for AutoPilot Options Trader
"""

import json
import os
from pathlib import Path
from typing import Dict, Any
import logging
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    """Configuration manager for the trading application."""
    
    def __init__(self, config_file: str = "config.json"):
        self.config_file = Path(config_file)
        self.logger = logging.getLogger(__name__)
        self._config = self._load_config()
        
    def _load_config(self) -> Dict[str, Any]:
        """Load configuration from file or create default."""
        if self.config_file.exists():
            try:
                with open(self.config_file, 'r') as f:
                    config = json.load(f)
                self.logger.info(f"Loaded configuration from {self.config_file}")
                return config
            except Exception as e:
                self.logger.error(f"Error loading config: {e}")
                return self._create_default_config()
        else:
            return self._create_default_config()
    
    def _create_default_config(self) -> Dict[str, Any]:
        """Create default configuration."""
        default_config = {
            # API Configuration
            "alpaca": {
                "api_key": os.getenv("ALPACA_API_KEY", ""),
                "secret_key": os.getenv("ALPACA_SECRET_KEY", ""),
                "base_url": "https://paper-api.alpaca.markets",  # Paper trading by default
                "data_url": "https://data.alpaca.markets"
            },
            
            # Trading Configuration
            "trading": {
                "paper_trading": True,
                "max_trades_per_day": 3,
                "max_position_size": 1000,  # Max $ per trade
                "risk_per_trade": 0.02,  # 2% of account per trade
                "stop_loss_percent": 0.5,  # 50% loss triggers stop
                "take_profit_percent": 1.0,  # 100% gain triggers take profit
                "min_days_to_expiry": 1,
                "max_days_to_expiry": 30,
                "otm_percent_range": [0.05, 0.15]  # 5-15% out of the money
            },
            
            # Screening Configuration
            "screening": {
                "min_market_cap": 10_000_000_000,  # $10B
                "min_volume": 1_000_000,  # 1M shares
                "min_option_volume": 100,
                "min_open_interest": 500,
                "max_bid_ask_spread": 0.10,  # 10 cents max spread
                "ttm_squeeze_lookback": 20,
                "momentum_lookback": 12,
                "rsi_period": 14,
                "rsi_oversold": 30,
                "rsi_overbought": 70
            },
            
            # Data Sources
            "data": {
                "primary_source": "alpaca",
                "backup_sources": ["polygon", "tradier"],
                "update_interval": 60,  # seconds
                "market_hours_only": True
            },
            
            # Logging
            "logging": {
                "level": "INFO",
                "file": "logs/trading.log",
                "max_file_size": 10_000_000,  # 10MB
                "backup_count": 5
            },
            
            # GUI Settings
            "gui": {
                "theme": "DarkBlue3",
                "window_size": [1200, 800],
                "refresh_interval": 1000,  # milliseconds
                "show_notifications": True
            }
        }
        
        # Save default config
        self._save_config(default_config)
        self.logger.info(f"Created default configuration at {self.config_file}")
        return default_config
    
    def _save_config(self, config: Dict[str, Any]):
        """Save configuration to file."""
        try:
            # Create directory if it doesn't exist
            self.config_file.parent.mkdir(parents=True, exist_ok=True)
            
            with open(self.config_file, 'w') as f:
                json.dump(config, f, indent=4)
        except Exception as e:
            self.logger.error(f"Error saving config: {e}")
    
    def get(self, key: str, default=None):
        """Get configuration value using dot notation."""
        keys = key.split('.')
        value = self._config
        
        for k in keys:
            if isinstance(value, dict) and k in value:
                value = value[k]
            else:
                return default
        
        return value
    
    def set(self, key: str, value: Any):
        """Set configuration value using dot notation."""
        keys = key.split('.')
        config = self._config
        
        for k in keys[:-1]:
            if k not in config:
                config[k] = {}
            config = config[k]
        
        config[keys[-1]] = value
        self._save_config(self._config)
    
    # Convenience properties
    @property
    def paper_trading(self) -> bool:
        return self.get('trading.paper_trading', True)
    
    @property
    def alpaca_api_key(self) -> str:
        return self.get('alpaca.api_key', '')
    
    @property
    def alpaca_secret_key(self) -> str:
        return self.get('alpaca.secret_key', '')
    
    @property
    def alpaca_base_url(self) -> str:
        return self.get('alpaca.base_url', 'https://paper-api.alpaca.markets')
    
    @property
    def max_trades_per_day(self) -> int:
        return self.get('trading.max_trades_per_day', 3)
    
    @property
    def gui_theme(self) -> str:
        return self.get('gui.theme', 'DarkBlue3')
