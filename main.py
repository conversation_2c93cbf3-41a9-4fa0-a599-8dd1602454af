#!/usr/bin/env python3
"""
AutoPilot Options Trader - Main Entry Point
An AI-powered options trading platform for total beginners.
"""

import sys
import os
import logging
from datetime import datetime
import asyncio
from pathlib import Path

# Add project root to path
project_root = Path(__file__).parent
sys.path.insert(0, str(project_root))

from gui import TradingGUI
from config import Config
from utils.logger import setup_logging

def main():
    """Main entry point for the AutoPilot Options Trader."""
    
    # Setup logging
    setup_logging()
    logger = logging.getLogger(__name__)
    
    try:
        # Load configuration
        config = Config()
        logger.info("AutoPilot Options Trader starting up...")
        logger.info(f"Trading mode: {'PAPER' if config.paper_trading else 'LIVE'}")
        
        # Initialize and run GUI
        app = TradingGUI(config)
        app.run()
        
    except KeyboardInterrupt:
        logger.info("Application interrupted by user")
    except Exception as e:
        logger.error(f"Fatal error: {e}", exc_info=True)
        print(f"Fatal error: {e}")
        sys.exit(1)
    finally:
        logger.info("AutoPilot Options Trader shutting down...")

if __name__ == "__main__":
    main()
