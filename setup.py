#!/usr/bin/env python3
"""
Setup script for AutoPilot Options Trader
"""

import os
import sys
import subprocess
from pathlib import Path

def check_python_version():
    """Check if Python version is compatible."""
    if sys.version_info < (3, 8):
        print("❌ Python 3.8 or higher is required")
        print(f"Current version: {sys.version}")
        return False
    print(f"✅ Python version: {sys.version.split()[0]}")
    return True

def install_requirements():
    """Install required packages."""
    print("📦 Installing required packages...")
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "-r", "requirements.txt"])
        print("✅ All packages installed successfully")
        return True
    except subprocess.CalledProcessError as e:
        print(f"❌ Error installing packages: {e}")
        return False

def create_directories():
    """Create necessary directories."""
    directories = ["logs", "data", "strategies", "utils"]
    
    for directory in directories:
        Path(directory).mkdir(exist_ok=True)
        print(f"✅ Created directory: {directory}")

def setup_env_file():
    """Setup environment file."""
    env_file = Path(".env")
    env_example = Path(".env.example")
    
    if not env_file.exists() and env_example.exists():
        print("📝 Setting up environment file...")
        print("Please edit .env file with your API keys after setup completes")
        
        # Copy example to .env
        with open(env_example, 'r') as src, open(env_file, 'w') as dst:
            dst.write(src.read())
        
        print("✅ Created .env file from template")
    elif env_file.exists():
        print("✅ .env file already exists")
    else:
        print("⚠️  No .env.example file found")

def check_api_keys():
    """Check if API keys are configured."""
    env_file = Path(".env")
    
    if not env_file.exists():
        print("⚠️  .env file not found. Please create it with your API keys.")
        return False
    
    with open(env_file, 'r') as f:
        content = f.read()
    
    if "your_paper_trading_api_key_here" in content:
        print("⚠️  Please update .env file with your actual Alpaca API keys")
        return False
    
    print("✅ API keys appear to be configured")
    return True

def run_initial_test():
    """Run a basic test to ensure everything works."""
    print("🧪 Running initial test...")
    try:
        # Test imports
        import PySimpleGUI as sg
        import pandas as pd
        import numpy as np
        import yfinance as yf
        
        print("✅ All core modules imported successfully")
        
        # Test configuration loading
        from config import Config
        config = Config()
        print("✅ Configuration loaded successfully")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False
    except Exception as e:
        print(f"❌ Test error: {e}")
        return False

def main():
    """Main setup function."""
    print("🚀 AutoPilot Options Trader Setup")
    print("=" * 40)
    
    # Check Python version
    if not check_python_version():
        return False
    
    # Create directories
    create_directories()
    
    # Install requirements
    if not install_requirements():
        return False
    
    # Setup environment file
    setup_env_file()
    
    # Run initial test
    if not run_initial_test():
        print("❌ Setup test failed. Please check the error messages above.")
        return False
    
    print("\n🎉 Setup completed successfully!")
    print("\n📋 Next Steps:")
    print("1. Edit .env file with your Alpaca API keys")
    print("2. Run: python main.py")
    print("3. Start with paper trading to test the system")
    print("\n⚠️  Important: Read README.md for full instructions")
    
    return True

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
