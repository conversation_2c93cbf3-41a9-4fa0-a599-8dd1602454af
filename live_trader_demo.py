#!/usr/bin/env python3
"""
Live Options Trader Demo - With Real API Integration
Shows the system working with Alpaca, FMP, and OpenAI APIs
"""

import sys
import asyncio
import logging
from datetime import datetime
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from config import Config
from utils.logger import setup_logging
from data.stock_universe import StockUniverse
from data.options_fetcher import OptionsDataFetcher
from strategies.ttm_squeeze import TTMSqueezeScreener
from trade_executor import TradeExecutor
from ai_explainer import AITradeExplainer

class LiveOptionsTrader:
    """Live options trading system with full API integration."""
    
    def __init__(self):
        setup_logging(log_level="INFO")
        self.config = Config()
        self.logger = logging.getLogger(__name__)
        
        # Initialize all components
        self.universe = StockUniverse(self.config)
        self.options_fetcher = OptionsDataFetcher(self.config)
        self.screener = TTMSqueezeScreener(self.config)
        self.executor = TradeExecutor(self.config)
        self.ai_explainer = AITradeExplainer(self.config)
        
        print("🤖 AutoPilot Options Trader - LIVE SYSTEM")
        print("=" * 50)
        print("✅ All APIs connected and ready!")
        print(f"📊 Trading Mode: {'PAPER' if self.config.paper_trading else 'LIVE'}")
        print()
    
    async def run_live_scan(self):
        """Run a live market scan with all features."""
        
        print("🔍 LIVE MARKET SCAN STARTING...")
        print("-" * 40)
        
        # Step 1: Load universe with FMP API
        print("📈 Loading S&P 500 universe...")
        symbols = await self.universe.load_sp500_symbols()
        print(f"✅ Loaded {len(symbols)} symbols from Financial Modeling Prep API")
        
        # Step 2: Screen for signals
        print(f"\n🎯 Screening top 25 symbols for TTM Squeeze signals...")
        test_symbols = symbols[:25]  # Scan top 25 for demo
        
        signals = await self.screener.scan_symbols(test_symbols)
        print(f"✅ Found {len(signals)} high-probability signals")
        
        # Generate market insight
        market_insight = self.ai_explainer.get_market_insight(test_symbols, len(signals))
        print(f"🧠 AI Market Insight: {market_insight}")
        print()
        
        if not signals:
            print("😴 No strong signals found in current market conditions.")
            print("   This is normal - the system waits for high-probability setups.")
            print("   In live mode, it would continue scanning every few minutes.")
            return
        
        # Step 3: Process top signals
        print("🎯 PROCESSING TOP SIGNALS")
        print("-" * 40)
        
        for i, signal in enumerate(signals[:3], 1):  # Process top 3 signals
            print(f"\n📊 Signal #{i}: {signal.symbol}")
            print(f"   Type: {signal.signal_type}")
            print(f"   Strength: {signal.strength:.2f}")
            print(f"   Price: ${signal.current_price:.2f}")
            print(f"   RSI: {signal.rsi:.1f}")
            
            # Find best option contract
            option_type = 'call' if signal.signal_type == 'bullish_breakout' else 'put'
            option_chain = await self.options_fetcher.get_option_chain(signal.symbol)
            
            if option_chain:
                best_contracts = self.options_fetcher.find_best_contracts(
                    signal.symbol, option_type
                )
                
                if best_contracts:
                    contract = best_contracts[0]
                    print(f"   📋 Best {option_type}: ${contract.strike} exp {contract.expiry.strftime('%m/%d/%Y')}")
                    print(f"   💰 Price: ${contract.mid_price:.2f} (${contract.mid_price * 100:.0f} per contract)")
                    
                    # Calculate position details
                    position_size = self._calculate_position_size(contract)
                    total_cost = contract.mid_price * position_size * 100
                    
                    print(f"   📊 Position: {position_size} contracts (${total_cost:.0f} total)")
                    
                    # Generate AI explanation
                    explanation = self.ai_explainer.explain_trade(
                        signal, contract, position_size, total_cost
                    )
                    print(f"\n🧠 AI EXPLANATION:")
                    print(f"   {explanation}")
                    
                    # Execute trade (paper trading)
                    if self.config.paper_trading:
                        print(f"\n💼 EXECUTING PAPER TRADE...")
                        position = await self.executor.execute_signal(signal, contract)
                        
                        if position:
                            print(f"   ✅ Trade executed successfully!")
                            print(f"   📈 Position: {position.symbol}")
                            print(f"   💰 Entry: ${position.entry_price:.2f}")
                            print(f"   🛡️ Stop Loss: ${position.stop_loss:.2f}")
                            print(f"   🎯 Take Profit: ${position.take_profit:.2f}")
                        else:
                            print(f"   ❌ Trade execution failed")
                    else:
                        print(f"\n⚠️  LIVE TRADING DISABLED - Would execute real trade here")
                else:
                    print(f"   ❌ No suitable {option_type} contracts found")
            else:
                print(f"   ❌ No option data available")
        
        # Step 4: Show portfolio status
        await self.show_portfolio_status()
        
        print("\n🎉 LIVE SCAN COMPLETE!")
        print("   In production, this would run continuously every few minutes")
        print("   monitoring for new opportunities and managing existing positions.")
    
    def _calculate_position_size(self, contract) -> int:
        """Calculate position size for demo."""
        # Use same logic as trade executor but simplified
        account_size = 25000  # $25k paper account
        risk_per_trade = 0.02  # 2% risk
        max_risk = account_size * risk_per_trade  # $500 max risk
        
        contract_cost = contract.mid_price * 100
        max_contracts = int(max_risk / contract_cost)
        
        return max(1, min(max_contracts, 5))  # 1-5 contracts
    
    async def show_portfolio_status(self):
        """Show current portfolio status."""
        print("\n📊 PORTFOLIO STATUS")
        print("-" * 40)
        
        positions = self.executor.get_open_positions()
        daily_pnl = self.executor.get_daily_pnl()
        
        print(f"📈 Open Positions: {len(positions)}")
        print(f"💰 Today's P&L: ${daily_pnl:.2f}")
        
        if positions:
            print("\n📋 Position Details:")
            for pos in positions:
                print(f"   {pos.underlying}: {pos.quantity} contracts")
                print(f"   Entry: ${pos.entry_price:.2f} | Current: ${pos.current_price:.2f}")
                print(f"   P&L: ${pos.unrealized_pnl:.2f}")
                print()
        
        # Show recent trades
        print("📝 Recent Trade Log:")
        log_file = Path("logs/trade_log.csv")
        if log_file.exists():
            try:
                with open(log_file, 'r') as f:
                    lines = f.readlines()
                    if len(lines) > 1:
                        for line in lines[-3:]:  # Last 3 trades
                            if line.strip() and not line.startswith('timestamp'):
                                parts = line.strip().split(',')
                                if len(parts) >= 8:
                                    timestamp = parts[0].split('T')[1][:8]  # Time only
                                    symbol = parts[1]
                                    action = parts[2]
                                    option_type = parts[3]
                                    cost = parts[8]
                                    print(f"   [{timestamp}] {action.upper()} {symbol} {option_type.upper()} - ${cost}")
            except Exception as e:
                print(f"   Error reading log: {e}")
        else:
            print("   No trades yet today")
    
    async def run_position_monitoring(self):
        """Demonstrate position monitoring."""
        print("\n🔄 POSITION MONITORING DEMO")
        print("-" * 40)
        
        positions = self.executor.get_open_positions()
        
        if not positions:
            print("   No positions to monitor")
            return
        
        print(f"   Monitoring {len(positions)} position(s)...")
        
        # Simulate position updates
        for i in range(3):  # 3 update cycles
            print(f"\n   Update #{i+1}:")
            await self.executor.update_positions()
            
            for pos in positions:
                if pos.status == 'open':
                    print(f"   {pos.underlying}: ${pos.current_price:.2f} (P&L: ${pos.unrealized_pnl:.2f})")
                else:
                    print(f"   {pos.underlying}: CLOSED - {pos.status} (P&L: ${pos.realized_pnl:.2f})")
                    
                    # Generate exit explanation
                    exit_explanation = self.ai_explainer.explain_exit(
                        pos.underlying, pos.status, pos.realized_pnl
                    )
                    print(f"   🧠 {exit_explanation}")
            
            if i < 2:  # Don't sleep on last iteration
                await asyncio.sleep(2)
        
        print("\n   Position monitoring complete!")

async def main():
    """Main function."""
    try:
        trader = LiveOptionsTrader()
        
        print("🚀 STARTING LIVE TRADING DEMO")
        print("   This uses your real API keys but stays in paper trading mode")
        print()
        
        # Run live market scan
        await trader.run_live_scan()
        
        # Demonstrate position monitoring
        await trader.run_position_monitoring()
        
        print("\n" + "=" * 50)
        print("🎉 LIVE TRADING DEMO COMPLETE!")
        print()
        print("✅ What just happened:")
        print("   • Loaded 500+ real S&P 500 symbols via FMP API")
        print("   • Scanned for TTM Squeeze signals using real market data")
        print("   • Found and analyzed option contracts")
        print("   • Generated AI explanations for trade decisions")
        print("   • Executed paper trades with full risk management")
        print("   • Monitored positions with real-time P&L tracking")
        print()
        print("🚀 TO GO LIVE:")
        print("   1. Change 'paper_trading' to false in config.json")
        print("   2. Verify your Alpaca account has options trading enabled")
        print("   3. Start with small position sizes")
        print("   4. Monitor trades closely")
        print()
        print("⚠️  REMEMBER: Options trading involves substantial risk!")
        
    except KeyboardInterrupt:
        print("\n⏹️  Demo interrupted by user")
    except Exception as e:
        print(f"\n💥 Demo error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    print("🤖 AutoPilot Options Trader - Live System Demo")
    print("   Using real APIs: Alpaca + FMP + OpenAI")
    print("   Paper trading mode for safety")
    print()
    
    asyncio.run(main())
