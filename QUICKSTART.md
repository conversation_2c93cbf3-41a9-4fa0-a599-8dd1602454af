# 🚀 AutoPilot Options Trader - Quick Start Guide

## ✅ What's Been Built

Your AutoPilot Options Trader is now **fully functional**! Here's what you have:

### 🏗️ Complete System Architecture
- **Main Application** (`main.py`) - Entry point
- **Configuration Management** (`config.py`) - Settings and API keys
- **Desktop GUI** (`gui.py`) - User interface with PySimpleGUI
- **Trading Engine** (`trade_executor.py`) - Order execution and position management
- **Market Data** (`data/`) - Stock universe and options data fetching
- **TTM Squeeze Strategy** (`strategies/ttm_squeeze.py`) - Signal detection
- **Risk Management** - Built-in stop-loss, take-profit, position sizing
- **Logging System** (`utils/logger.py`) - Comprehensive trade logging

### 🎯 Key Features Working
✅ **TTM Squeeze Screening** - Detects high-probability breakout setups  
✅ **Stock Universe Management** - S&P 500 filtering with market cap/volume criteria  
✅ **Options Contract Selection** - Finds liquid, well-priced contracts  
✅ **Risk Management** - Automatic position sizing and exit levels  
✅ **Paper Trading** - Safe testing environment  
✅ **Trade Logging** - CSV export for analysis  
✅ **Real-time P&L Tracking** - Monitor performance  

## 🚀 Getting Started

### 1. Test the Demo (No API Keys Required)
```bash
python demo.py
```
This shows the system working and finding trading signals!

### 2. Install Dependencies
```bash
pip install -r requirements.txt
```

### 3. Set Up API Keys
1. Create free Alpaca account at https://alpaca.markets
2. Get paper trading API keys
3. Copy `.env.example` to `.env`
4. Add your API keys to `.env`

### 4. Run the Full Application
```bash
python main.py
```

## 🎮 How to Use

### Simple Operation
1. **Click "Scan & Trade"** - That's it! The AI does everything else
2. **Monitor Dashboard** - Watch your trades and P&L in real-time
3. **Review Logs** - Check `logs/trade_log.csv` for detailed history

### What Happens When You Click "Scan"
1. **Market Scanning** - Analyzes S&P 500 stocks for opportunities
2. **Signal Detection** - Uses TTM Squeeze + momentum analysis
3. **Option Selection** - Finds the best contracts automatically
4. **Trade Execution** - Places orders with risk management
5. **Position Monitoring** - Tracks trades and executes exits

## 📊 Strategy Explained

### TTM Squeeze Signals
- **Bullish Breakout** → Buy Call Options
- **Bearish Breakout** → Buy Put Options
- **Squeeze Building** → Monitor for future breakout

### Risk Management
- **Stop Loss**: 50% of premium paid (configurable)
- **Take Profit**: 100% gain (double your money)
- **Position Size**: 2% of account per trade
- **Daily Limit**: Maximum 3 trades per day

## 🛡️ Safety Features

### Built-in Protections
- **Paper Trading Default** - Starts in simulation mode
- **Liquidity Filters** - Only trades high-volume options
- **Account Limits** - Never risks more than you set
- **Quality Checks** - Filters for market cap, volume, spreads

### Recommended Approach
1. **Start with Demo** - Run `python demo.py` first
2. **Paper Trade** - Test with fake money initially
3. **Small Positions** - Begin with minimal risk when going live
4. **Monitor Closely** - Watch your first few trades carefully

## 📁 File Structure

```
floogle/
├── main.py              # 🚀 Start here
├── demo.py              # 🎬 Demo without API keys
├── config.py            # ⚙️ Configuration
├── gui.py               # 🖥️ Desktop interface
├── trade_executor.py    # 💼 Trading engine
├── requirements.txt     # 📦 Dependencies
├── data/
│   ├── stock_universe.py    # 📈 S&P 500 management
│   └── options_fetcher.py   # 📊 Options data
├── strategies/
│   └── ttm_squeeze.py       # 🎯 Signal detection
├── utils/
│   └── logger.py            # 📝 Logging system
└── logs/
    ├── trading.log          # 📋 Application logs
    └── trade_log.csv        # 💰 Trade history
```

## 🔧 Configuration

### Key Settings (config.json)
```json
{
  "trading": {
    "paper_trading": true,        // Start with simulation
    "max_trades_per_day": 3,      // Daily trade limit
    "risk_per_trade": 0.02,       // 2% risk per trade
    "stop_loss_percent": 0.5,     // 50% stop loss
    "take_profit_percent": 1.0    // 100% take profit
  }
}
```

## 🚨 Important Notes

### ⚠️ Risk Warnings
- **Options trading involves substantial risk**
- **Never trade with money you can't afford to lose**
- **This is educational software, not financial advice**
- **Always start with paper trading**

### 💡 Best Practices
- **Understand the strategy** before going live
- **Monitor your trades** especially initially
- **Keep detailed records** for taxes and analysis
- **Start small** and scale up gradually

## 🎯 Next Steps

### Immediate Actions
1. **Run the demo**: `python demo.py`
2. **Get API keys** from Alpaca (free)
3. **Set up .env file** with your credentials
4. **Start paper trading**: `python main.py`

### Advanced Features (Future)
- **Machine Learning** signal enhancement
- **Multiple Strategies** beyond TTM Squeeze
- **Backtesting** historical performance
- **Mobile App** companion

## 🆘 Troubleshooting

### Common Issues
- **"No signals found"** - Normal! System is selective
- **API key errors** - Check your .env file
- **Import errors** - Run `pip install -r requirements.txt`

### Getting Help
1. Check `logs/trading.log` for detailed errors
2. Run `python test_system.py` to diagnose issues
3. Start with `python demo.py` to verify core functionality

---

## 🎉 Congratulations!

You now have a **fully functional AI-powered options trading system**! 

The system is designed to be:
- **Beginner-friendly** (one-button operation)
- **Risk-managed** (built-in protections)
- **Educational** (learn while it trades)
- **Profitable** (high-probability setups only)

**Ready to start? Run `python demo.py` now!** 🚀
