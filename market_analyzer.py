"""
Advanced Market Analyzer for Options Trading
Analyzes volatility, market regime, and technical conditions
"""

import numpy as np
import pandas as pd
import yfinance as yf
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
import logging
from scipy import stats
import asyncio

from trading_types import MarketRegime, VolatilityProfile, MarketConditions

class MarketAnalyzer:
    """Advanced market analysis for options trading."""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Analysis parameters
        self.lookback_periods = {
            'short': 20,
            'medium': 50,
            'long': 200
        }
        
        # Volatility thresholds
        self.vol_thresholds = {
            'low': 0.15,    # 15% annualized
            'medium': 0.25, # 25% annualized
            'high': 0.40    # 40% annualized
        }
    
    async def analyze_symbol(self, symbol: str) -> Optional[MarketConditions]:
        """Comprehensive analysis of a symbol's market conditions."""
        try:
            # Get price data
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period="1y", interval="1d")
            
            if len(hist) < 50:
                self.logger.warning(f"Insufficient data for {symbol}")
                return None
            
            # Get options data for IV analysis
            options_data = await self._get_options_data(ticker)
            
            # Analyze components
            volatility_profile = self._analyze_volatility(hist, options_data)
            market_regime = self._determine_market_regime(hist, volatility_profile)
            price_trend = self._analyze_price_trend(hist)
            support_resistance = self._find_support_resistance(hist)
            earnings_info = await self._get_earnings_info(ticker)
            volume_profile = self._analyze_volume(hist)
            
            return MarketConditions(
                regime=market_regime,
                volatility_profile=volatility_profile,
                price_trend=price_trend,
                support_resistance=support_resistance,
                earnings_date=earnings_info.get('date'),
                days_to_earnings=earnings_info.get('days'),
                volume_profile=volume_profile
            )
            
        except Exception as e:
            self.logger.error(f"Error analyzing {symbol}: {e}")
            return None
    
    def _analyze_volatility(self, hist: pd.DataFrame, options_data: Dict) -> VolatilityProfile:
        """Analyze volatility characteristics."""
        # Calculate historical volatility
        returns = hist['Close'].pct_change().dropna()
        hist_vol = returns.std() * np.sqrt(252)  # Annualized
        
        # Get implied volatility from options
        implied_vol = options_data.get('avg_iv', hist_vol * 1.2)  # Default to 20% premium
        
        # Calculate IV rank and percentile
        iv_history = self._get_iv_history(hist, implied_vol)
        iv_rank = self._calculate_percentile(implied_vol, iv_history)
        iv_percentile = iv_rank  # Simplified
        
        # Volatility skew analysis
        vol_skew = self._calculate_vol_skew(options_data)
        
        # Term structure
        term_structure = self._analyze_term_structure(options_data)
        
        return VolatilityProfile(
            implied_vol=implied_vol,
            historical_vol=hist_vol,
            iv_rank=iv_rank,
            iv_percentile=iv_percentile,
            vol_skew=vol_skew,
            term_structure=term_structure
        )
    
    def _determine_market_regime(self, hist: pd.DataFrame, vol_profile: VolatilityProfile) -> MarketRegime:
        """Determine current market regime."""
        prices = hist['Close']
        
        # Trend analysis
        sma_20 = prices.rolling(20).mean()
        sma_50 = prices.rolling(50).mean()
        current_price = prices.iloc[-1]
        
        # Volatility regime
        if vol_profile.iv_rank > 80:
            return MarketRegime.HIGH_VOLATILITY
        elif vol_profile.iv_rank < 20:
            return MarketRegime.LOW_VOLATILITY
        
        # Trend regime
        if current_price > sma_20.iloc[-1] > sma_50.iloc[-1]:
            trend_strength = (current_price - sma_50.iloc[-1]) / sma_50.iloc[-1]
            if trend_strength > 0.05:  # 5% above 50-day MA
                return MarketRegime.TRENDING_UP
        elif current_price < sma_20.iloc[-1] < sma_50.iloc[-1]:
            trend_strength = (sma_50.iloc[-1] - current_price) / sma_50.iloc[-1]
            if trend_strength > 0.05:  # 5% below 50-day MA
                return MarketRegime.TRENDING_DOWN
        
        # Default to ranging if no clear trend
        return MarketRegime.RANGING
    
    def _analyze_price_trend(self, hist: pd.DataFrame) -> float:
        """Analyze price trend strength (-1 to 1)."""
        prices = hist['Close']
        
        # Linear regression slope
        x = np.arange(len(prices))
        slope, _, r_value, _, _ = stats.linregress(x, prices)
        
        # Normalize slope by price level
        normalized_slope = slope / prices.mean()
        
        # Weight by R-squared (trend strength)
        trend_score = normalized_slope * (r_value ** 2)
        
        # Clamp to [-1, 1]
        return max(-1, min(1, trend_score * 100))
    
    def _find_support_resistance(self, hist: pd.DataFrame) -> Tuple[float, float]:
        """Find key support and resistance levels."""
        prices = hist['Close']
        highs = hist['High']
        lows = hist['Low']
        
        # Recent price action (last 50 days)
        recent_data = hist.tail(50)
        
        # Support: Recent significant lows
        support_candidates = recent_data['Low'].nsmallest(5)
        support = support_candidates.mean()
        
        # Resistance: Recent significant highs
        resistance_candidates = recent_data['High'].nlargest(5)
        resistance = resistance_candidates.mean()
        
        return (support, resistance)
    
    async def _get_earnings_info(self, ticker) -> Dict:
        """Get earnings date information."""
        try:
            # Try to get earnings calendar
            calendar = ticker.calendar
            if calendar is not None and not calendar.empty:
                next_earnings = calendar.index[0]
                days_to_earnings = (next_earnings - datetime.now()).days
                return {
                    'date': next_earnings,
                    'days': days_to_earnings
                }
        except:
            pass
        
        return {'date': None, 'days': None}
    
    def _analyze_volume(self, hist: pd.DataFrame) -> Dict[str, float]:
        """Analyze volume characteristics."""
        volume = hist['Volume']
        
        avg_volume = volume.rolling(20).mean().iloc[-1]
        current_volume = volume.iloc[-1]
        volume_ratio = current_volume / avg_volume if avg_volume > 0 else 1.0
        
        # Volume trend
        volume_trend = volume.rolling(10).mean().iloc[-1] / volume.rolling(30).mean().iloc[-1]
        
        return {
            'avg_volume': avg_volume,
            'current_volume': current_volume,
            'volume_ratio': volume_ratio,
            'volume_trend': volume_trend
        }
    
    async def _get_options_data(self, ticker) -> Dict:
        """Get options data for IV analysis."""
        try:
            # Get option chain
            options = ticker.options
            if not options:
                return {'avg_iv': 0.25}  # Default IV
            
            # Get nearest expiration
            nearest_exp = options[0]
            option_chain = ticker.option_chain(nearest_exp)
            
            # Calculate average IV
            calls_iv = option_chain.calls['impliedVolatility'].mean()
            puts_iv = option_chain.puts['impliedVolatility'].mean()
            avg_iv = (calls_iv + puts_iv) / 2
            
            return {
                'avg_iv': avg_iv,
                'calls_iv': calls_iv,
                'puts_iv': puts_iv,
                'expiration': nearest_exp,
                'calls_data': option_chain.calls,
                'puts_data': option_chain.puts
            }
            
        except Exception as e:
            self.logger.warning(f"Could not get options data: {e}")
            return {'avg_iv': 0.25}  # Default IV
    
    def _get_iv_history(self, hist: pd.DataFrame, current_iv: float) -> List[float]:
        """Simulate IV history for ranking calculation."""
        # In production, this would use actual IV history
        # For now, simulate based on price volatility
        returns = hist['Close'].pct_change().dropna()
        rolling_vol = returns.rolling(20).std() * np.sqrt(252)
        
        # Simulate IV as HV + premium
        iv_history = rolling_vol * np.random.uniform(1.1, 1.4, len(rolling_vol))
        return iv_history.dropna().tolist()
    
    def _calculate_percentile(self, current_value: float, history: List[float]) -> float:
        """Calculate percentile rank of current value in history."""
        if not history:
            return 50.0
        
        rank = stats.percentileofscore(history, current_value)
        return rank
    
    def _calculate_vol_skew(self, options_data: Dict) -> float:
        """Calculate volatility skew."""
        try:
            calls_data = options_data.get('calls_data')
            puts_data = options_data.get('puts_data')
            
            if calls_data is None or puts_data is None:
                return 0.0
            
            # OTM put IV vs OTM call IV
            otm_puts = puts_data[puts_data['inTheMoney'] == False]
            otm_calls = calls_data[calls_data['inTheMoney'] == False]
            
            if len(otm_puts) > 0 and len(otm_calls) > 0:
                put_iv = otm_puts['impliedVolatility'].mean()
                call_iv = otm_calls['impliedVolatility'].mean()
                skew = put_iv - call_iv
                return skew
            
        except Exception as e:
            self.logger.debug(f"Error calculating vol skew: {e}")
        
        return 0.0
    
    def _analyze_term_structure(self, options_data: Dict) -> Dict[int, float]:
        """Analyze volatility term structure."""
        # Simplified - would use multiple expirations in production
        avg_iv = options_data.get('avg_iv', 0.25)
        
        # Simulate term structure
        return {
            30: avg_iv,
            60: avg_iv * 0.95,
            90: avg_iv * 0.90,
            120: avg_iv * 0.85
        }
