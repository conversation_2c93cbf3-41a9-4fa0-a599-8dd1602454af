"""
Multi-Leg Options Execution Engine
Advanced order execution with slippage minimization and smart routing
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
import logging
from datetime import datetime, timedelta
import asyncio
from dataclasses import dataclass

from trading_types import StrategyType, StrategyScore, Position, Greeks, MarketConditions

@dataclass
class OrderLeg:
    """Individual leg of a multi-leg order."""
    symbol: str
    option_type: str  # 'call' or 'put'
    strike: float
    expiry: datetime
    action: str  # 'buy' or 'sell'
    quantity: int
    limit_price: Optional[float] = None
    order_type: str = 'limit'

@dataclass
class ExecutionResult:
    """Result of order execution."""
    success: bool
    position: Optional[Position]
    fill_price: float
    slippage: float
    commission: float
    execution_time: datetime
    error_message: Optional[str] = None

class MultiLegExecutor:
    """Advanced multi-leg options execution engine."""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Execution parameters
        self.max_slippage = 0.05  # 5% max slippage
        self.order_timeout = 30   # 30 seconds timeout
        self.min_time_between_legs = 0.1  # 100ms between legs
        
        # Commission structure
        self.commission_per_contract = 0.65
        self.commission_per_leg = 0.50
        
        # Strategy builders
        self.strategy_builders = {
            StrategyType.LONG_CALL: self._build_long_call,
            StrategyType.LONG_PUT: self._build_long_put,
            StrategyType.BULL_CALL_SPREAD: self._build_bull_call_spread,
            StrategyType.BEAR_PUT_SPREAD: self._build_bear_put_spread,
            StrategyType.IRON_CONDOR: self._build_iron_condor,
            StrategyType.LONG_STRADDLE: self._build_long_straddle,
            StrategyType.LONG_STRANGLE: self._build_long_strangle,
            StrategyType.SHORT_STRADDLE: self._build_short_straddle,
            StrategyType.COVERED_CALL: self._build_covered_call,
            StrategyType.CASH_SECURED_PUT: self._build_cash_secured_put,
            StrategyType.IRON_BUTTERFLY: self._build_iron_butterfly,
            StrategyType.BUTTERFLY_SPREAD: self._build_butterfly_spread,
            StrategyType.CALENDAR_SPREAD: self._build_calendar_spread,
        }
    
    async def execute_strategy(self, symbol: str, strategy_score: StrategyScore,
                             market_conditions: MarketConditions) -> Optional[Position]:
        """Execute a complete options strategy."""
        
        try:
            # Build order legs
            order_legs = await self._build_strategy_legs(
                symbol, strategy_score.strategy, market_conditions
            )
            
            if not order_legs:
                self.logger.error(f"Failed to build legs for {strategy_score.strategy.value}")
                return None
            
            # Calculate position sizing
            position_size = self._calculate_position_size(strategy_score, market_conditions)
            
            # Adjust quantities
            for leg in order_legs:
                leg.quantity = int(leg.quantity * position_size)
                if leg.quantity == 0:
                    leg.quantity = 1  # Minimum 1 contract
            
            # Execute multi-leg order
            execution_result = await self._execute_multi_leg_order(order_legs, strategy_score.strategy)
            
            if execution_result.success:
                return execution_result.position
            else:
                self.logger.error(f"Execution failed: {execution_result.error_message}")
                return None
                
        except Exception as e:
            self.logger.error(f"Error executing strategy {strategy_score.strategy.value}: {e}")
            return None
    
    async def _build_strategy_legs(self, symbol: str, strategy: StrategyType,
                                 market_conditions: MarketConditions) -> List[OrderLeg]:
        """Build order legs for a specific strategy."""
        
        builder = self.strategy_builders.get(strategy)
        if not builder:
            self.logger.error(f"No builder found for strategy {strategy.value}")
            return []
        
        return await builder(symbol, market_conditions)
    
    async def _execute_multi_leg_order(self, order_legs: List[OrderLeg], 
                                     strategy: StrategyType) -> ExecutionResult:
        """Execute multi-leg order with smart routing."""
        
        start_time = datetime.now()
        total_cost = 0
        total_slippage = 0
        total_commission = 0
        executed_legs = []
        
        try:
            # For complex strategies, use simultaneous execution
            if len(order_legs) > 2:
                return await self._execute_simultaneous_legs(order_legs, strategy)
            
            # For simple strategies, execute legs sequentially with timing optimization
            for i, leg in enumerate(order_legs):
                # Add small delay between legs to avoid market impact
                if i > 0:
                    await asyncio.sleep(self.min_time_between_legs)
                
                # Execute individual leg
                leg_result = await self._execute_single_leg(leg)
                
                if not leg_result['success']:
                    # Rollback previous legs if possible
                    await self._rollback_executed_legs(executed_legs)
                    return ExecutionResult(
                        success=False,
                        position=None,
                        fill_price=0,
                        slippage=0,
                        commission=0,
                        execution_time=start_time,
                        error_message=f"Failed to execute leg {i+1}: {leg_result['error']}"
                    )
                
                executed_legs.append(leg_result)
                total_cost += leg_result['cost']
                total_slippage += leg_result['slippage']
                total_commission += leg_result['commission']
            
            # Create position object
            position = self._create_position_from_legs(
                order_legs[0].symbol, strategy, executed_legs, total_cost, total_commission
            )
            
            return ExecutionResult(
                success=True,
                position=position,
                fill_price=total_cost,
                slippage=total_slippage,
                commission=total_commission,
                execution_time=datetime.now()
            )
            
        except Exception as e:
            self.logger.error(f"Multi-leg execution error: {e}")
            return ExecutionResult(
                success=False,
                position=None,
                fill_price=0,
                slippage=0,
                commission=0,
                execution_time=start_time,
                error_message=str(e)
            )
    
    async def _execute_simultaneous_legs(self, order_legs: List[OrderLeg], 
                                       strategy: StrategyType) -> ExecutionResult:
        """Execute all legs simultaneously for complex strategies."""
        
        # Create coroutines for all legs
        leg_tasks = [self._execute_single_leg(leg) for leg in order_legs]
        
        # Execute all legs concurrently
        leg_results = await asyncio.gather(*leg_tasks, return_exceptions=True)
        
        # Check for failures
        total_cost = 0
        total_slippage = 0
        total_commission = 0
        
        for i, result in enumerate(leg_results):
            if isinstance(result, Exception) or not result.get('success', False):
                return ExecutionResult(
                    success=False,
                    position=None,
                    fill_price=0,
                    slippage=0,
                    commission=0,
                    execution_time=datetime.now(),
                    error_message=f"Simultaneous execution failed on leg {i+1}"
                )
            
            total_cost += result['cost']
            total_slippage += result['slippage']
            total_commission += result['commission']
        
        # Create position
        position = self._create_position_from_legs(
            order_legs[0].symbol, strategy, leg_results, total_cost, total_commission
        )
        
        return ExecutionResult(
            success=True,
            position=position,
            fill_price=total_cost,
            slippage=total_slippage,
            commission=total_commission,
            execution_time=datetime.now()
        )
    
    async def _execute_single_leg(self, leg: OrderLeg) -> Dict[str, Any]:
        """Execute a single option leg."""
        
        # Simulate execution with realistic slippage and timing
        base_price = leg.limit_price or self._estimate_option_price(leg)
        
        # Calculate slippage based on market conditions
        slippage_factor = np.random.uniform(0.01, 0.03)  # 1-3% slippage
        if leg.action == 'buy':
            fill_price = base_price * (1 + slippage_factor)
        else:
            fill_price = base_price * (1 - slippage_factor)
        
        # Calculate cost and commission
        cost = fill_price * leg.quantity * 100  # Options are per 100 shares
        if leg.action == 'sell':
            cost = -cost  # Credit for selling
        
        commission = self.commission_per_contract * leg.quantity
        slippage = abs(fill_price - base_price) * leg.quantity * 100
        
        # Simulate execution delay
        await asyncio.sleep(np.random.uniform(0.1, 0.5))
        
        return {
            'success': True,
            'leg': leg,
            'fill_price': fill_price,
            'cost': cost,
            'commission': commission,
            'slippage': slippage,
            'execution_time': datetime.now()
        }
    
    def _create_position_from_legs(self, symbol: str, strategy: StrategyType,
                                 executed_legs: List[Dict], total_cost: float,
                                 total_commission: float) -> Position:
        """Create position object from executed legs."""
        
        # Calculate position metrics
        max_profit, max_loss = self._calculate_position_metrics(strategy, executed_legs)
        breakeven_points = self._calculate_breakeven_points(strategy, executed_legs)
        greeks = self._calculate_position_greeks(executed_legs)
        
        # Determine days to expiry (use shortest expiry)
        min_expiry = min(leg['leg'].expiry for leg in executed_legs)
        days_to_expiry = (min_expiry - datetime.now()).days
        
        # Create leg details for position
        legs = []
        for leg_result in executed_legs:
            leg = leg_result['leg']
            legs.append({
                'option_type': leg.option_type,
                'strike': leg.strike,
                'expiry': leg.expiry,
                'action': leg.action,
                'quantity': leg.quantity,
                'fill_price': leg_result['fill_price']
            })
        
        return Position(
            symbol=symbol,
            strategy=strategy,
            legs=legs,
            entry_time=datetime.now(),
            total_cost=total_cost + total_commission,
            max_profit=max_profit,
            max_loss=max_loss,
            breakeven_points=breakeven_points,
            greeks=greeks,
            target_profit=max_profit * 0.5,  # 50% of max profit
            stop_loss=max_loss * 0.5,        # 50% of max loss
            days_to_expiry=days_to_expiry
        )
    
    def _estimate_option_price(self, leg: OrderLeg) -> float:
        """Estimate option price using simplified Black-Scholes."""
        # Simplified pricing - would use real option pricing in production
        
        # Mock prices based on moneyness and time
        days_to_expiry = (leg.expiry - datetime.now()).days
        time_value = max(0.01, days_to_expiry / 365 * 0.25)  # 25% IV assumption
        
        # Assume current stock price around strike for simplicity
        stock_price = leg.strike
        
        if leg.option_type == 'call':
            intrinsic = max(0, stock_price - leg.strike)
        else:
            intrinsic = max(0, leg.strike - stock_price)
        
        return intrinsic + time_value
    
    def _calculate_position_size(self, strategy_score: StrategyScore,
                               market_conditions: MarketConditions) -> float:
        """Calculate position size multiplier."""
        # Base size on strategy confidence and risk
        base_size = 1.0
        
        # Adjust for confidence
        confidence_multiplier = strategy_score.confidence
        
        # Adjust for volatility (smaller size in high vol)
        vol_adjustment = 1.0
        if market_conditions.volatility_profile.iv_rank > 75:
            vol_adjustment = 0.8
        elif market_conditions.volatility_profile.iv_rank < 25:
            vol_adjustment = 1.2
        
        return base_size * confidence_multiplier * vol_adjustment
    
    # Strategy builders (simplified implementations)
    async def _build_long_call(self, symbol: str, market_conditions: MarketConditions) -> List[OrderLeg]:
        """Build long call strategy."""
        current_price = 100  # Would get from market data
        strike = current_price * 1.05  # 5% OTM
        expiry = datetime.now() + timedelta(days=30)
        
        return [OrderLeg(
            symbol=f"{symbol}_{expiry.strftime('%y%m%d')}C{int(strike*1000):08d}",
            option_type='call',
            strike=strike,
            expiry=expiry,
            action='buy',
            quantity=1
        )]
    
    async def _build_long_put(self, symbol: str, market_conditions: MarketConditions) -> List[OrderLeg]:
        """Build long put strategy."""
        current_price = 100
        strike = current_price * 0.95  # 5% OTM
        expiry = datetime.now() + timedelta(days=30)
        
        return [OrderLeg(
            symbol=f"{symbol}_{expiry.strftime('%y%m%d')}P{int(strike*1000):08d}",
            option_type='put',
            strike=strike,
            expiry=expiry,
            action='buy',
            quantity=1
        )]
    
    async def _build_iron_condor(self, symbol: str, market_conditions: MarketConditions) -> List[OrderLeg]:
        """Build iron condor strategy."""
        current_price = 100
        expiry = datetime.now() + timedelta(days=30)
        
        # 4-leg strategy: sell call spread + sell put spread
        legs = [
            # Sell call spread
            OrderLeg(f"{symbol}_{expiry.strftime('%y%m%d')}C{int(105*1000):08d}", 'call', 105, expiry, 'sell', 1),
            OrderLeg(f"{symbol}_{expiry.strftime('%y%m%d')}C{int(110*1000):08d}", 'call', 110, expiry, 'buy', 1),
            # Sell put spread
            OrderLeg(f"{symbol}_{expiry.strftime('%y%m%d')}P{int(95*1000):08d}", 'put', 95, expiry, 'sell', 1),
            OrderLeg(f"{symbol}_{expiry.strftime('%y%m%d')}P{int(90*1000):08d}", 'put', 90, expiry, 'buy', 1),
        ]
        
        return legs
    
    # Additional strategy builders would be implemented similarly...
    async def _build_bull_call_spread(self, symbol: str, market_conditions: MarketConditions) -> List[OrderLeg]:
        return []  # Placeholder
    
    async def _build_bear_put_spread(self, symbol: str, market_conditions: MarketConditions) -> List[OrderLeg]:
        return []  # Placeholder
    
    async def _build_long_straddle(self, symbol: str, market_conditions: MarketConditions) -> List[OrderLeg]:
        return []  # Placeholder
    
    async def _build_long_strangle(self, symbol: str, market_conditions: MarketConditions) -> List[OrderLeg]:
        return []  # Placeholder
    
    async def _build_short_straddle(self, symbol: str, market_conditions: MarketConditions) -> List[OrderLeg]:
        return []  # Placeholder
    
    async def _build_covered_call(self, symbol: str, market_conditions: MarketConditions) -> List[OrderLeg]:
        return []  # Placeholder
    
    async def _build_cash_secured_put(self, symbol: str, market_conditions: MarketConditions) -> List[OrderLeg]:
        return []  # Placeholder
    
    async def _build_iron_butterfly(self, symbol: str, market_conditions: MarketConditions) -> List[OrderLeg]:
        return []  # Placeholder
    
    async def _build_butterfly_spread(self, symbol: str, market_conditions: MarketConditions) -> List[OrderLeg]:
        return []  # Placeholder
    
    async def _build_calendar_spread(self, symbol: str, market_conditions: MarketConditions) -> List[OrderLeg]:
        return []  # Placeholder
