#!/usr/bin/env python3
"""
Simple GUI Launcher for AutoPilot Options Trader
Choose between demo mode and full application
"""

import PySimpleGUI as sg
import subprocess
import sys
import os
from pathlib import Path

def create_launcher_layout():
    """Create the launcher window layout."""
    
    layout = [
        [sg.Text("🤖 AutoPilot Options Trader", font=("Arial", 20, "bold"), justification='center')],
        [sg.Text("Choose how you want to run the application:", font=("Arial", 12), justification='center')],
        [sg.HSeparator()],
        
        [sg.Text("🎬 Demo Mode", font=("Arial", 14, "bold"))],
        [sg.Text("• Shows the GUI interface working")],
        [sg.Text("• Simulates trading signals and execution")],
        [sg.Text("• No API keys required")],
        [sg.Text("• Safe to explore and test")],
        [sg.Button("Launch Demo GUI", key="-DEMO-GUI-", size=(20, 2), button_color=("white", "blue"))],
        
        [sg.HSeparator()],
        
        [sg.Text("🚀 Full Application", font=("Arial", 14, "bold"))],
        [sg.Text("• Complete trading system")],
        [sg.Text("• Requires Alpaca API keys")],
        [sg.Text("• Can execute real trades")],
        [sg.Text("• Paper trading enabled by default")],
        [sg.Button("Launch Full App", key="-FULL-APP-", size=(20, 2), button_color=("white", "green"))],
        
        [sg.HSeparator()],
        
        [sg.Text("📊 Terminal Demo", font=("Arial", 14, "bold"))],
        [sg.Text("• Shows strategy working in terminal")],
        [sg.Text("• No GUI, just text output")],
        [sg.Text("• Good for testing core functionality")],
        [sg.Button("Run Terminal Demo", key="-TERMINAL-DEMO-", size=(20, 2), button_color=("white", "orange"))],
        
        [sg.HSeparator()],
        
        [sg.Button("Exit", key="-EXIT-", size=(10, 1))]
    ]
    
    return layout

def launch_demo_gui():
    """Launch the GUI demo."""
    try:
        print("🖥️  Launching GUI Demo...")
        subprocess.Popen([sys.executable, "gui_demo.py"])
        return True
    except Exception as e:
        sg.popup_error(f"Error launching GUI demo: {e}")
        return False

def launch_full_app():
    """Launch the full application."""
    try:
        # Check if .env file exists
        if not Path(".env").exists():
            result = sg.popup_yes_no(
                "No .env file found!\n\n"
                "The full application requires Alpaca API keys.\n"
                "Do you want to continue anyway?\n\n"
                "(The app will run in demo mode without API keys)",
                title="Missing API Keys"
            )
            if result != "Yes":
                return False
        
        print("🚀 Launching Full Application...")
        subprocess.Popen([sys.executable, "main.py"])
        return True
    except Exception as e:
        sg.popup_error(f"Error launching full app: {e}")
        return False

def launch_terminal_demo():
    """Launch the terminal demo."""
    try:
        print("📊 Launching Terminal Demo...")
        subprocess.Popen([sys.executable, "demo.py"], creationflags=subprocess.CREATE_NEW_CONSOLE)
        return True
    except Exception as e:
        sg.popup_error(f"Error launching terminal demo: {e}")
        return False

def main():
    """Main launcher function."""
    sg.theme('DarkBlue3')
    
    layout = create_launcher_layout()
    window = sg.Window(
        "AutoPilot Options Trader - Launcher",
        layout,
        size=(500, 600),
        element_justification='center',
        finalize=True
    )
    
    print("🚀 AutoPilot Options Trader Launcher")
    print("   Choose your preferred way to run the application")
    
    while True:
        event, values = window.read()
        
        if event in (sg.WIN_CLOSED, "-EXIT-"):
            break
        elif event == "-DEMO-GUI-":
            if launch_demo_gui():
                sg.popup("GUI Demo launched!\nCheck for the new window that opened.", title="Demo Started")
        elif event == "-FULL-APP-":
            if launch_full_app():
                sg.popup("Full application launched!\nCheck for the new window that opened.", title="App Started")
        elif event == "-TERMINAL-DEMO-":
            if launch_terminal_demo():
                sg.popup("Terminal demo launched!\nCheck for the new console window.", title="Demo Started")
    
    window.close()
    print("👋 Launcher closed")

if __name__ == "__main__":
    main()
