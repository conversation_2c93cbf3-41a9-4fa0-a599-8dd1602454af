"""
Trade Executor - Handles option trade execution via Alpaca
"""

import logging
import asyncio
from datetime import datetime, timedelta
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
import alpaca_trade_api as tradeapi
from alpaca_trade_api.rest import TimeFrame

from data.options_fetcher import OptionContract
from strategies.ttm_squeeze import SqueezeSignal
from utils.logger import get_trade_logger

@dataclass
class TradeOrder:
    """Represents a trade order."""
    symbol: str
    underlying: str
    action: str  # 'buy', 'sell'
    quantity: int
    order_type: str  # 'market', 'limit'
    limit_price: Optional[float] = None
    stop_loss: Optional[float] = None
    take_profit: Optional[float] = None
    signal_strength: float = 0.0
    notes: str = ""

@dataclass
class Position:
    """Represents an open position."""
    symbol: str
    underlying: str
    quantity: int
    entry_price: float
    current_price: float
    entry_time: datetime
    stop_loss: float
    take_profit: float
    unrealized_pnl: float
    realized_pnl: float = 0.0
    status: str = "open"  # open, closed, stopped_out, profit_taken

class TradeExecutor:
    """Handles trade execution and position management."""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        self.trade_logger = get_trade_logger()
        
        # Initialize Alpaca API
        self.api = tradeapi.REST(
            key_id=config.alpaca_api_key,
            secret_key=config.alpaca_secret_key,
            base_url=config.alpaca_base_url,
            api_version='v2'
        )
        
        self.positions = {}
        self.daily_trades = 0
        self.last_trade_date = None
        
    async def execute_signal(self, signal: SqueezeSignal, 
                           option_contract: OptionContract) -> Optional[Position]:
        """Execute a trade based on a signal and option contract."""
        try:
            # Check daily trade limit
            if not self._can_trade():
                self.logger.warning("Daily trade limit reached")
                return None
            
            # Determine trade direction
            if signal.signal_type == 'bullish_breakout':
                action = 'buy'
                option_type = 'call'
            elif signal.signal_type == 'bearish_breakout':
                action = 'buy'
                option_type = 'put'
            else:
                self.logger.info(f"No trade action for signal type: {signal.signal_type}")
                return None
            
            # Calculate position size
            quantity = self._calculate_position_size(option_contract)
            if quantity <= 0:
                self.logger.warning("Position size calculation resulted in 0 quantity")
                return None
            
            # Calculate stop loss and take profit
            stop_loss, take_profit = self._calculate_exit_levels(option_contract)
            
            # Create trade order
            order = TradeOrder(
                symbol=option_contract.symbol,
                underlying=option_contract.underlying,
                action=action,
                quantity=quantity,
                order_type='market',  # Use market orders for simplicity
                stop_loss=stop_loss,
                take_profit=take_profit,
                signal_strength=signal.strength,
                notes=f"TTM Squeeze {signal.signal_type} - {signal.notes}"
            )
            
            # Execute the trade
            position = await self._execute_order(order, option_contract)
            
            if position:
                self.positions[position.symbol] = position
                self._update_daily_trade_count()
                
                # Log the trade
                self._log_trade(order, option_contract, position)
                
                self.logger.info(f"Executed trade: {order.action} {order.quantity} {order.symbol}")
            
            return position
            
        except Exception as e:
            self.logger.error(f"Error executing signal: {e}")
            return None
    
    async def _execute_order(self, order: TradeOrder, 
                           contract: OptionContract) -> Optional[Position]:
        """Execute the actual order via Alpaca."""
        try:
            # For paper trading, we'll simulate the order execution
            if self.config.paper_trading:
                return self._simulate_order_execution(order, contract)
            
            # Real trading implementation would go here
            # This would use Alpaca's actual options trading API
            self.logger.warning("Live trading not implemented - using simulation")
            return self._simulate_order_execution(order, contract)
            
        except Exception as e:
            self.logger.error(f"Error executing order: {e}")
            return None
    
    def _simulate_order_execution(self, order: TradeOrder, 
                                contract: OptionContract) -> Position:
        """Simulate order execution for paper trading."""
        # Use mid price for execution
        execution_price = contract.mid_price
        
        # Create position
        position = Position(
            symbol=order.symbol,
            underlying=order.underlying,
            quantity=order.quantity,
            entry_price=execution_price,
            current_price=execution_price,
            entry_time=datetime.now(),
            stop_loss=order.stop_loss or 0,
            take_profit=order.take_profit or 0,
            unrealized_pnl=0.0
        )
        
        return position
    
    def _calculate_position_size(self, contract: OptionContract) -> int:
        """Calculate position size based on risk management rules."""
        try:
            # Get account info
            account = self.api.get_account()
            buying_power = float(account.buying_power)
            
            # Maximum position size from config
            max_position = self.config.get('trading.max_position_size', 1000)
            
            # Risk per trade (percentage of account)
            risk_percent = self.config.get('trading.risk_per_trade', 0.02)
            risk_amount = buying_power * risk_percent
            
            # Option premium cost
            option_cost = contract.mid_price * 100  # Options are per 100 shares
            
            # Calculate quantity based on risk
            max_contracts_by_risk = int(risk_amount / option_cost)
            max_contracts_by_position = int(max_position / option_cost)
            
            # Take the minimum
            quantity = min(max_contracts_by_risk, max_contracts_by_position, 10)  # Max 10 contracts
            
            return max(1, quantity)  # At least 1 contract
            
        except Exception as e:
            self.logger.error(f"Error calculating position size: {e}")
            return 1
    
    def _calculate_exit_levels(self, contract: OptionContract) -> Tuple[float, float]:
        """Calculate stop loss and take profit levels."""
        entry_price = contract.mid_price
        
        # Stop loss percentage
        stop_loss_pct = self.config.get('trading.stop_loss_percent', 0.5)
        stop_loss = entry_price * (1 - stop_loss_pct)
        
        # Take profit percentage
        take_profit_pct = self.config.get('trading.take_profit_percent', 1.0)
        take_profit = entry_price * (1 + take_profit_pct)
        
        return round(stop_loss, 2), round(take_profit, 2)
    
    def _can_trade(self) -> bool:
        """Check if we can execute more trades today."""
        today = datetime.now().date()
        
        # Reset daily counter if new day
        if self.last_trade_date != today:
            self.daily_trades = 0
            self.last_trade_date = today
        
        max_trades = self.config.max_trades_per_day
        return self.daily_trades < max_trades
    
    def _update_daily_trade_count(self):
        """Update the daily trade counter."""
        today = datetime.now().date()
        if self.last_trade_date != today:
            self.daily_trades = 1
            self.last_trade_date = today
        else:
            self.daily_trades += 1
    
    def _log_trade(self, order: TradeOrder, contract: OptionContract, position: Position):
        """Log trade details to CSV."""
        try:
            log_entry = (
                f"{datetime.now().isoformat()},"
                f"{order.underlying},"
                f"{order.action},"
                f"{contract.option_type},"
                f"{contract.strike},"
                f"{contract.expiry.date()},"
                f"{order.quantity},"
                f"{contract.mid_price},"
                f"{contract.mid_price * order.quantity * 100},"
                f"{order.stop_loss},"
                f"{order.take_profit},"
                f"{order.signal_strength:.2f},"
                f'"{order.notes}"'
            )
            
            self.trade_logger.info(log_entry)
            
        except Exception as e:
            self.logger.error(f"Error logging trade: {e}")
    
    async def update_positions(self):
        """Update all open positions with current prices."""
        try:
            for symbol, position in self.positions.items():
                if position.status == 'open':
                    # Get current option price (simulated)
                    current_price = await self._get_current_option_price(position)
                    
                    if current_price:
                        position.current_price = current_price
                        position.unrealized_pnl = self._calculate_pnl(position)
                        
                        # Check for stop loss or take profit
                        await self._check_exit_conditions(position)
            
        except Exception as e:
            self.logger.error(f"Error updating positions: {e}")
    
    async def _get_current_option_price(self, position: Position) -> Optional[float]:
        """Get current option price (simulated)."""
        try:
            # In a real implementation, this would fetch current option prices
            # For simulation, we'll add some random movement
            import random
            
            # Simulate price movement (±5% random walk)
            change_percent = random.uniform(-0.05, 0.05)
            new_price = position.current_price * (1 + change_percent)
            
            return max(0.01, new_price)  # Minimum 1 cent
            
        except Exception:
            return None
    
    def _calculate_pnl(self, position: Position) -> float:
        """Calculate unrealized P&L for a position."""
        price_diff = position.current_price - position.entry_price
        return price_diff * position.quantity * 100  # Options are per 100 shares
    
    async def _check_exit_conditions(self, position: Position):
        """Check if position should be closed due to stop loss or take profit."""
        try:
            current_price = position.current_price
            
            # Check stop loss
            if position.stop_loss > 0 and current_price <= position.stop_loss:
                await self._close_position(position, 'stopped_out')
                return
            
            # Check take profit
            if position.take_profit > 0 and current_price >= position.take_profit:
                await self._close_position(position, 'profit_taken')
                return
            
        except Exception as e:
            self.logger.error(f"Error checking exit conditions: {e}")
    
    async def _close_position(self, position: Position, reason: str):
        """Close a position."""
        try:
            position.status = reason
            position.realized_pnl = position.unrealized_pnl
            
            self.logger.info(f"Closed position {position.symbol}: {reason}, P&L: ${position.realized_pnl:.2f}")
            
            # Log the exit
            exit_log = (
                f"{datetime.now().isoformat()},"
                f"{position.underlying},"
                f"sell,"
                f"exit,"
                f"0,"
                f"{datetime.now().date()},"
                f"{position.quantity},"
                f"{position.current_price},"
                f"{position.current_price * position.quantity * 100},"
                f"0,"
                f"0,"
                f"0,"
                f'"{reason} - P&L: ${position.realized_pnl:.2f}"'
            )
            
            self.trade_logger.info(exit_log)
            
        except Exception as e:
            self.logger.error(f"Error closing position: {e}")
    
    def get_open_positions(self) -> List[Position]:
        """Get all open positions."""
        return [pos for pos in self.positions.values() if pos.status == 'open']
    
    def get_daily_pnl(self) -> float:
        """Get today's total P&L."""
        today = datetime.now().date()
        total_pnl = 0.0
        
        for position in self.positions.values():
            if position.entry_time.date() == today:
                if position.status == 'open':
                    total_pnl += position.unrealized_pnl
                else:
                    total_pnl += position.realized_pnl
        
        return total_pnl
