"""
TTM Squeeze Strategy Implementation
Detects squeeze conditions and momentum for options trading signals
"""

import pandas as pd
import numpy as np
import yfinance as yf
import logging
from typing import Dict, List, Optional, Tuple
from dataclasses import dataclass
from datetime import datetime, timedelta

@dataclass
class SqueezeSignal:
    """Represents a TTM Squeeze signal."""
    symbol: str
    timestamp: datetime
    signal_type: str  # 'bullish_breakout', 'bearish_breakout', 'squeeze_building'
    strength: float  # 0-1 signal strength
    current_price: float
    squeeze_active: bool
    momentum_direction: str  # 'up', 'down', 'neutral'
    momentum_value: float
    bollinger_squeeze: bool
    keltner_squeeze: bool
    volume_surge: bool
    rsi: float
    notes: str

class TTMSqueezeScreener:
    """TTM Squeeze screener for options trading signals."""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Strategy parameters
        self.bb_period = 20
        self.bb_std = 2.0
        self.kc_period = 20
        self.kc_multiplier = 1.5
        self.momentum_period = 12
        self.rsi_period = 14
        
    async def scan_symbols(self, symbols: List[str]) -> List[SqueezeSignal]:
        """Scan multiple symbols for TTM Squeeze signals."""
        signals = []
        
        self.logger.info(f"Scanning {len(symbols)} symbols for TTM Squeeze signals...")
        
        for symbol in symbols:
            try:
                signal = await self.analyze_symbol(symbol)
                if signal and signal.strength > 0.3:  # Minimum signal strength
                    signals.append(signal)
                    
            except Exception as e:
                self.logger.debug(f"Error analyzing {symbol}: {e}")
                continue
        
        # Sort by signal strength
        signals.sort(key=lambda x: x.strength, reverse=True)
        
        self.logger.info(f"Found {len(signals)} TTM Squeeze signals")
        return signals
    
    async def analyze_symbol(self, symbol: str) -> Optional[SqueezeSignal]:
        """Analyze a single symbol for TTM Squeeze conditions."""
        try:
            # Get price data
            ticker = yf.Ticker(symbol)
            hist = ticker.history(period="3mo", interval="1d")
            
            if len(hist) < 50:  # Need enough data
                return None
            
            # Calculate indicators
            df = self._calculate_indicators(hist)
            
            if df is None or len(df) < 20:
                return None
            
            # Get latest values
            latest = df.iloc[-1]
            prev = df.iloc[-2] if len(df) > 1 else latest
            
            # Determine squeeze conditions
            squeeze_active = self._is_squeeze_active(latest)
            momentum_direction, momentum_value = self._get_momentum(df)
            volume_surge = self._detect_volume_surge(df)
            
            # Determine signal type and strength
            signal_type, strength = self._evaluate_signal(
                df, squeeze_active, momentum_direction, volume_surge
            )
            
            if strength < 0.1:  # Too weak
                return None
            
            # Create signal
            signal = SqueezeSignal(
                symbol=symbol,
                timestamp=datetime.now(),
                signal_type=signal_type,
                strength=strength,
                current_price=float(latest['close']),
                squeeze_active=squeeze_active,
                momentum_direction=momentum_direction,
                momentum_value=momentum_value,
                bollinger_squeeze=latest['bb_squeeze'],
                keltner_squeeze=latest['kc_squeeze'],
                volume_surge=volume_surge,
                rsi=float(latest['rsi']),
                notes=self._generate_notes(latest, momentum_direction, volume_surge)
            )
            
            return signal
            
        except Exception as e:
            self.logger.debug(f"Error in analyze_symbol for {symbol}: {e}")
            return None
    
    def _calculate_indicators(self, df: pd.DataFrame) -> Optional[pd.DataFrame]:
        """Calculate all technical indicators using pandas/numpy."""
        try:
            df = df.copy()

            # Ensure we have OHLCV columns
            df.columns = [col.lower() for col in df.columns]

            # Bollinger Bands
            df['bb_middle'] = df['close'].rolling(window=self.bb_period).mean()
            bb_std = df['close'].rolling(window=self.bb_period).std()
            df['bb_upper'] = df['bb_middle'] + (bb_std * self.bb_std)
            df['bb_lower'] = df['bb_middle'] - (bb_std * self.bb_std)

            # Keltner Channels (using EMA and ATR)
            df['kc_middle'] = df['close'].ewm(span=self.kc_period).mean()

            # Calculate ATR (Average True Range)
            high_low = df['high'] - df['low']
            high_close = np.abs(df['high'] - df['close'].shift())
            low_close = np.abs(df['low'] - df['close'].shift())
            true_range = np.maximum(high_low, np.maximum(high_close, low_close))
            atr = true_range.rolling(window=self.kc_period).mean()

            df['kc_upper'] = df['kc_middle'] + (atr * self.kc_multiplier)
            df['kc_lower'] = df['kc_middle'] - (atr * self.kc_multiplier)

            # Squeeze conditions
            df['bb_squeeze'] = (df['bb_lower'] > df['kc_lower']) & (df['bb_upper'] < df['kc_upper'])
            df['kc_squeeze'] = df['bb_squeeze']  # Same condition

            # Momentum (TTM Squeeze momentum)
            highest_high = df['high'].rolling(window=self.kc_period).max()
            lowest_low = df['low'].rolling(window=self.kc_period).min()
            df['momentum'] = df['close'] - ((highest_high + lowest_low) / 2 + df['kc_middle']) / 2

            # RSI (Relative Strength Index)
            delta = df['close'].diff()
            gain = (delta.where(delta > 0, 0)).rolling(window=self.rsi_period).mean()
            loss = (-delta.where(delta < 0, 0)).rolling(window=self.rsi_period).mean()
            rs = gain / loss
            df['rsi'] = 100 - (100 / (1 + rs))

            # EMAs for trend
            df['ema_20'] = df['close'].ewm(span=20).mean()
            df['ema_50'] = df['close'].ewm(span=50).mean()

            # Volume indicators
            df['volume_sma'] = df['volume'].rolling(window=20).mean()
            df['volume_ratio'] = df['volume'] / df['volume_sma']

            return df

        except Exception as e:
            self.logger.error(f"Error calculating indicators: {e}")
            return None
    
    def _is_squeeze_active(self, row) -> bool:
        """Check if squeeze is currently active."""
        return bool(row['bb_squeeze'])
    
    def _get_momentum(self, df: pd.DataFrame) -> Tuple[str, float]:
        """Get momentum direction and value."""
        try:
            # Get recent momentum values
            recent_momentum = df['momentum'].tail(5)
            current_momentum = recent_momentum.iloc[-1]
            prev_momentum = recent_momentum.iloc[-2] if len(recent_momentum) > 1 else current_momentum
            
            # Determine direction
            if current_momentum > 0 and current_momentum > prev_momentum:
                direction = 'up'
            elif current_momentum < 0 and current_momentum < prev_momentum:
                direction = 'down'
            else:
                direction = 'neutral'
            
            return direction, float(current_momentum)
            
        except Exception:
            return 'neutral', 0.0
    
    def _detect_volume_surge(self, df: pd.DataFrame) -> bool:
        """Detect if there's a volume surge."""
        try:
            latest_volume_ratio = df['volume_ratio'].iloc[-1]
            return latest_volume_ratio > 1.5  # 50% above average
        except Exception:
            return False
    
    def _evaluate_signal(self, df: pd.DataFrame, squeeze_active: bool, 
                        momentum_direction: str, volume_surge: bool) -> Tuple[str, float]:
        """Evaluate the signal type and strength."""
        try:
            latest = df.iloc[-1]
            prev = df.iloc[-2] if len(df) > 1 else latest
            
            strength = 0.0
            signal_type = 'neutral'
            
            # Check for squeeze breakout
            if prev['bb_squeeze'] and not latest['bb_squeeze']:
                # Squeeze is ending - potential breakout
                if momentum_direction == 'up':
                    signal_type = 'bullish_breakout'
                    strength += 0.6
                elif momentum_direction == 'down':
                    signal_type = 'bearish_breakout'
                    strength += 0.6
            
            # Check for squeeze building
            elif squeeze_active:
                signal_type = 'squeeze_building'
                strength += 0.3
            
            # Add momentum strength
            momentum_strength = abs(latest['momentum']) / latest['close'] * 100
            strength += min(0.3, momentum_strength / 2)
            
            # Add volume surge bonus
            if volume_surge:
                strength += 0.2
            
            # Add trend alignment bonus
            if latest['close'] > latest['ema_20'] > latest['ema_50']:
                if signal_type == 'bullish_breakout':
                    strength += 0.1
            elif latest['close'] < latest['ema_20'] < latest['ema_50']:
                if signal_type == 'bearish_breakout':
                    strength += 0.1
            
            # RSI confirmation
            rsi = latest['rsi']
            if signal_type == 'bullish_breakout' and 30 < rsi < 70:
                strength += 0.1
            elif signal_type == 'bearish_breakout' and 30 < rsi < 70:
                strength += 0.1
            
            return signal_type, min(1.0, strength)
            
        except Exception as e:
            self.logger.error(f"Error evaluating signal: {e}")
            return 'neutral', 0.0
    
    def _generate_notes(self, row, momentum_direction: str, volume_surge: bool) -> str:
        """Generate descriptive notes for the signal."""
        notes = []
        
        if row['bb_squeeze']:
            notes.append("TTM Squeeze active")
        else:
            notes.append("No squeeze")
        
        notes.append(f"Momentum: {momentum_direction}")
        
        if volume_surge:
            notes.append("Volume surge detected")
        
        rsi = row['rsi']
        if rsi < 30:
            notes.append("RSI oversold")
        elif rsi > 70:
            notes.append("RSI overbought")
        
        return "; ".join(notes)
