"""
GUI Interface for AutoPilot Options Trader
Simple desktop interface with PySimpleGUI
"""

import PySimpleGUI as sg
import logging
import asyncio
import threading
from datetime import datetime
from typing import List, Dict, Optional
import pandas as pd

from data.stock_universe import StockUniverse
from data.options_fetcher import OptionsDataFetcher
from strategies.ttm_squeeze import TTMSqueezeScreener
from trade_executor import TradeExecutor

class TradingGUI:
    """Main GUI application for the options trader."""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.stock_universe = StockUniverse(config)
        self.options_fetcher = OptionsDataFetcher(config)
        self.screener = TTMSqueezeScreener(config)
        self.executor = TradeExecutor(config)
        
        # GUI state
        self.window = None
        self.scanning = False
        self.last_scan_time = None
        self.scan_results = []
        
        # Set theme
        sg.theme(config.gui_theme)
        
        # Start background tasks
        self.background_thread = None
        self.stop_background = False
    
    def create_layout(self):
        """Create the main GUI layout."""
        
        # Header section
        header_layout = [
            [sg.Text("🤖 AutoPilot Options Trader", font=("Arial", 20, "bold"))],
            [sg.Text("AI-Powered Options Trading for Beginners", font=("Arial", 12))],
            [sg.HSeparator()]
        ]
        
        # Control section
        control_layout = [
            [sg.Text("Trading Controls", font=("Arial", 14, "bold"))],
            [
                sg.Button("🔍 Scan & Trade", key="-SCAN-", size=(15, 2), 
                         button_color=("white", "green"), font=("Arial", 12, "bold")),
                sg.Button("⏹️ Stop", key="-STOP-", size=(10, 2), 
                         button_color=("white", "red"), disabled=True),
                sg.Button("⚙️ Settings", key="-SETTINGS-", size=(10, 2))
            ],
            [sg.Text("", key="-STATUS-", font=("Arial", 10), text_color="blue")]
        ]
        
        # Statistics section
        stats_layout = [
            [sg.Text("Today's Statistics", font=("Arial", 14, "bold"))],
            [
                sg.Text("Trades:", size=(10, 1)), 
                sg.Text("0", key="-TRADES-COUNT-", size=(5, 1)),
                sg.Text("P&L:", size=(10, 1)), 
                sg.Text("$0.00", key="-PNL-", size=(10, 1), text_color="green")
            ],
            [
                sg.Text("Win Rate:", size=(10, 1)), 
                sg.Text("0%", key="-WIN-RATE-", size=(5, 1)),
                sg.Text("Last Scan:", size=(10, 1)), 
                sg.Text("Never", key="-LAST-SCAN-", size=(15, 1))
            ]
        ]
        
        # Signals section
        signals_layout = [
            [sg.Text("Current Signals", font=("Arial", 14, "bold"))],
            [
                sg.Table(
                    values=[],
                    headings=["Symbol", "Signal", "Strength", "Price", "Notes"],
                    key="-SIGNALS-TABLE-",
                    auto_size_columns=True,
                    justification="left",
                    num_rows=8,
                    alternating_row_color="lightblue",
                    enable_events=True
                )
            ]
        ]
        
        # Positions section
        positions_layout = [
            [sg.Text("Open Positions", font=("Arial", 14, "bold"))],
            [
                sg.Table(
                    values=[],
                    headings=["Symbol", "Type", "Qty", "Entry", "Current", "P&L", "Status"],
                    key="-POSITIONS-TABLE-",
                    auto_size_columns=True,
                    justification="left",
                    num_rows=6,
                    alternating_row_color="lightgray"
                )
            ]
        ]
        
        # Log section
        log_layout = [
            [sg.Text("Activity Log", font=("Arial", 14, "bold"))],
            [
                sg.Multiline(
                    default_text="AutoPilot Options Trader initialized...\n",
                    key="-LOG-",
                    size=(80, 8),
                    autoscroll=True,
                    disabled=True,
                    font=("Courier", 9)
                )
            ]
        ]
        
        # Main layout
        layout = [
            [sg.Column(header_layout, justification="center")],
            [sg.HSeparator()],
            [
                sg.Column(control_layout, vertical_alignment="top"),
                sg.VSeparator(),
                sg.Column(stats_layout, vertical_alignment="top")
            ],
            [sg.HSeparator()],
            [sg.Column(signals_layout, expand_x=True)],
            [sg.HSeparator()],
            [sg.Column(positions_layout, expand_x=True)],
            [sg.HSeparator()],
            [sg.Column(log_layout, expand_x=True)]
        ]
        
        return layout
    
    def run(self):
        """Run the main GUI loop."""
        try:
            layout = self.create_layout()
            
            self.window = sg.Window(
                "AutoPilot Options Trader",
                layout,
                size=self.config.get('gui.window_size', [1200, 800]),
                resizable=True,
                finalize=True
            )
            
            # Start background update thread
            self.start_background_tasks()
            
            self.log_message("GUI initialized successfully")
            
            # Main event loop
            while True:
                event, values = self.window.read(timeout=1000)
                
                if event == sg.WIN_CLOSED:
                    break
                elif event == "-SCAN-":
                    self.start_scan()
                elif event == "-STOP-":
                    self.stop_scan()
                elif event == "-SETTINGS-":
                    self.show_settings()
                elif event == "-SIGNALS-TABLE-":
                    self.handle_signal_selection(values)
                
                # Update GUI elements
                self.update_gui()
            
        except Exception as e:
            self.logger.error(f"GUI error: {e}")
            sg.popup_error(f"GUI Error: {e}")
        finally:
            self.cleanup()
    
    def start_scan(self):
        """Start the scanning process."""
        if self.scanning:
            return
        
        try:
            self.scanning = True
            self.window["-SCAN-"].update(disabled=True)
            self.window["-STOP-"].update(disabled=False)
            self.window["-STATUS-"].update("Scanning for opportunities...")
            
            self.log_message("Starting market scan...")
            
            # Run scan in background thread to avoid blocking GUI
            scan_thread = threading.Thread(target=self.run_scan_async)
            scan_thread.daemon = True
            scan_thread.start()
            
        except Exception as e:
            self.logger.error(f"Error starting scan: {e}")
            self.log_message(f"Error starting scan: {e}")
            self.scanning = False
            self.window["-SCAN-"].update(disabled=False)
            self.window["-STOP-"].update(disabled=True)
    
    def run_scan_async(self):
        """Run the scan in a separate thread."""
        try:
            # Create new event loop for this thread
            loop = asyncio.new_event_loop()
            asyncio.set_event_loop(loop)
            
            # Run the actual scan
            loop.run_until_complete(self.perform_scan())
            
        except Exception as e:
            self.logger.error(f"Error in scan thread: {e}")
            self.log_message(f"Scan error: {e}")
        finally:
            self.scanning = False
            if self.window:
                self.window["-SCAN-"].update(disabled=False)
                self.window["-STOP-"].update(disabled=True)
                self.window["-STATUS-"].update("Scan completed")
    
    async def perform_scan(self):
        """Perform the actual market scan and trading."""
        try:
            # Step 1: Update stock universe
            self.log_message("Loading stock universe...")
            symbols = await self.stock_universe.update_universe()
            
            if not symbols:
                self.log_message("No symbols found in universe")
                return
            
            self.log_message(f"Scanning {len(symbols)} symbols...")
            
            # Step 2: Screen for TTM Squeeze signals
            signals = await self.screener.scan_symbols(symbols[:50])  # Limit for demo
            
            if not signals:
                self.log_message("No trading signals found")
                return
            
            self.scan_results = signals
            self.last_scan_time = datetime.now()
            
            self.log_message(f"Found {len(signals)} potential signals")
            
            # Step 3: Execute trades for top signals
            trades_executed = 0
            for signal in signals[:3]:  # Top 3 signals
                if not self.scanning:  # Check if stopped
                    break
                
                try:
                    # Get option chain
                    option_chain = await self.options_fetcher.get_option_chain(signal.symbol)
                    
                    # Find best contract
                    option_type = 'call' if signal.signal_type == 'bullish_breakout' else 'put'
                    contracts = self.options_fetcher.find_best_contracts(
                        signal.symbol, option_type
                    )
                    
                    if contracts:
                        best_contract = contracts[0]
                        
                        # Execute trade
                        position = await self.executor.execute_signal(signal, best_contract)
                        
                        if position:
                            trades_executed += 1
                            self.log_message(f"Executed trade: {signal.symbol} {signal.signal_type}")
                        else:
                            self.log_message(f"Failed to execute trade for {signal.symbol}")
                    
                except Exception as e:
                    self.logger.error(f"Error processing signal {signal.symbol}: {e}")
                    continue
            
            self.log_message(f"Scan completed. Executed {trades_executed} trades.")
            
        except Exception as e:
            self.logger.error(f"Error in perform_scan: {e}")
            self.log_message(f"Scan error: {e}")
    
    def stop_scan(self):
        """Stop the current scan."""
        self.scanning = False
        self.window["-STATUS-"].update("Stopping scan...")
        self.log_message("Scan stopped by user")
    
    def show_settings(self):
        """Show settings dialog."""
        settings_layout = [
            [sg.Text("Trading Settings", font=("Arial", 14, "bold"))],
            [sg.Text("Max Trades per Day:"), sg.Input(str(self.config.max_trades_per_day), key="-MAX-TRADES-", size=(10, 1))],
            [sg.Text("Risk per Trade (%):"), sg.Input(str(self.config.get('trading.risk_per_trade', 0.02) * 100), key="-RISK-PCT-", size=(10, 1))],
            [sg.Text("Stop Loss (%):"), sg.Input(str(self.config.get('trading.stop_loss_percent', 0.5) * 100), key="-STOP-LOSS-", size=(10, 1))],
            [sg.Text("Take Profit (%):"), sg.Input(str(self.config.get('trading.take_profit_percent', 1.0) * 100), key="-TAKE-PROFIT-", size=(10, 1))],
            [sg.Checkbox("Paper Trading", default=self.config.paper_trading, key="-PAPER-TRADING-")],
            [sg.Button("Save", key="-SAVE-SETTINGS-"), sg.Button("Cancel", key="-CANCEL-SETTINGS-")]
        ]
        
        settings_window = sg.Window("Settings", settings_layout, modal=True)
        
        while True:
            event, values = settings_window.read()
            
            if event in (sg.WIN_CLOSED, "-CANCEL-SETTINGS-"):
                break
            elif event == "-SAVE-SETTINGS-":
                try:
                    # Update configuration
                    self.config.set('trading.max_trades_per_day', int(values["-MAX-TRADES-"]))
                    self.config.set('trading.risk_per_trade', float(values["-RISK-PCT-"]) / 100)
                    self.config.set('trading.stop_loss_percent', float(values["-STOP-LOSS-"]) / 100)
                    self.config.set('trading.take_profit_percent', float(values["-TAKE-PROFIT-"]) / 100)
                    self.config.set('trading.paper_trading', values["-PAPER-TRADING-"])
                    
                    sg.popup("Settings saved successfully!")
                    break
                    
                except ValueError as e:
                    sg.popup_error(f"Invalid input: {e}")
        
        settings_window.close()
    
    def handle_signal_selection(self, values):
        """Handle signal table selection."""
        try:
            if values["-SIGNALS-TABLE-"]:
                row = values["-SIGNALS-TABLE-"][0]
                if row < len(self.scan_results):
                    signal = self.scan_results[row]
                    self.show_signal_details(signal)
        except Exception as e:
            self.logger.error(f"Error handling signal selection: {e}")
    
    def show_signal_details(self, signal):
        """Show detailed information about a signal."""
        details = f"""
Signal Details for {signal.symbol}

Signal Type: {signal.signal_type}
Strength: {signal.strength:.2f}
Current Price: ${signal.current_price:.2f}
RSI: {signal.rsi:.1f}
Momentum: {signal.momentum_direction}

Squeeze Active: {signal.squeeze_active}
Volume Surge: {signal.volume_surge}

Notes: {signal.notes}
        """
        
        sg.popup_scrolled(details, title=f"Signal Details - {signal.symbol}", size=(50, 15))
    
    def update_gui(self):
        """Update GUI elements with current data."""
        try:
            # Update statistics
            positions = self.executor.get_open_positions()
            daily_pnl = self.executor.get_daily_pnl()
            
            self.window["-TRADES-COUNT-"].update(str(len(positions)))
            self.window["-PNL-"].update(f"${daily_pnl:.2f}")
            
            if self.last_scan_time:
                self.window["-LAST-SCAN-"].update(self.last_scan_time.strftime("%H:%M:%S"))
            
            # Update signals table
            if self.scan_results:
                signals_data = []
                for signal in self.scan_results[:10]:  # Top 10
                    signals_data.append([
                        signal.symbol,
                        signal.signal_type,
                        f"{signal.strength:.2f}",
                        f"${signal.current_price:.2f}",
                        signal.notes[:30] + "..." if len(signal.notes) > 30 else signal.notes
                    ])
                self.window["-SIGNALS-TABLE-"].update(values=signals_data)
            
            # Update positions table
            positions_data = []
            for position in positions:
                positions_data.append([
                    position.underlying,
                    position.symbol.split('C' if 'C' in position.symbol else 'P')[1] if 'C' in position.symbol or 'P' in position.symbol else 'N/A',
                    str(position.quantity),
                    f"${position.entry_price:.2f}",
                    f"${position.current_price:.2f}",
                    f"${position.unrealized_pnl:.2f}",
                    position.status
                ])
            self.window["-POSITIONS-TABLE-"].update(values=positions_data)
            
        except Exception as e:
            self.logger.debug(f"Error updating GUI: {e}")
    
    def start_background_tasks(self):
        """Start background tasks for position updates."""
        def background_worker():
            while not self.stop_background:
                try:
                    # Update positions every 30 seconds
                    loop = asyncio.new_event_loop()
                    asyncio.set_event_loop(loop)
                    loop.run_until_complete(self.executor.update_positions())
                    
                    # Sleep for 30 seconds
                    for _ in range(30):
                        if self.stop_background:
                            break
                        threading.Event().wait(1)
                        
                except Exception as e:
                    self.logger.error(f"Background task error: {e}")
        
        self.background_thread = threading.Thread(target=background_worker)
        self.background_thread.daemon = True
        self.background_thread.start()
    
    def log_message(self, message: str):
        """Add a message to the log."""
        try:
            timestamp = datetime.now().strftime("%H:%M:%S")
            log_entry = f"[{timestamp}] {message}\n"
            
            if self.window:
                current_log = self.window["-LOG-"].get()
                self.window["-LOG-"].update(current_log + log_entry)
            
            self.logger.info(message)
            
        except Exception as e:
            self.logger.error(f"Error logging message: {e}")
    
    def cleanup(self):
        """Cleanup resources."""
        try:
            self.stop_background = True
            self.scanning = False
            
            if self.background_thread and self.background_thread.is_alive():
                self.background_thread.join(timeout=2)
            
            if self.window:
                self.window.close()
                
            self.logger.info("GUI cleanup completed")
            
        except Exception as e:
            self.logger.error(f"Error during cleanup: {e}")
