"""
AI Trade Explainer - Uses OpenAI to explain trading decisions
"""

import os
import logging
from typing import Optional
import openai
from strategies.ttm_squeeze import SqueezeSignal
from data.options_fetcher import OptionContract

class AITradeExplainer:
    """Uses AI to explain trading decisions in plain English."""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize OpenAI
        self.client = None
        try:
            api_key = os.getenv('OPENAI_API_KEY')
            if api_key:
                self.client = openai.OpenAI(api_key=api_key)
                self.logger.info("OpenAI API initialized for trade explanations")
            else:
                self.logger.info("No OpenAI API key - explanations will be basic")
        except Exception as e:
            self.logger.warning(f"OpenAI initialization failed: {e}")
    
    def explain_trade(self, signal: SqueezeSignal, contract: OptionContract, 
                     position_size: int, total_cost: float) -> str:
        """Generate an AI explanation of the trade decision."""
        
        if self.client:
            return self._generate_ai_explanation(signal, contract, position_size, total_cost)
        else:
            return self._generate_basic_explanation(signal, contract, position_size, total_cost)
    
    def _generate_ai_explanation(self, signal: SqueezeSignal, contract: OptionContract,
                               position_size: int, total_cost: float) -> str:
        """Generate AI-powered explanation using OpenAI."""
        try:
            # Create prompt for AI
            prompt = f"""
You are an expert options trader explaining a trade decision to a beginner. 

TRADE DETAILS:
- Stock: {signal.symbol} (${signal.current_price:.2f})
- Signal: {signal.signal_type} (strength: {signal.strength:.2f})
- Option: {contract.option_type.upper()} ${contract.strike} expiring {contract.expiry.strftime('%B %d, %Y')}
- Position: {position_size} contracts at ${contract.mid_price:.2f} each
- Total Cost: ${total_cost:.2f}
- RSI: {signal.rsi:.1f}
- Momentum: {signal.momentum_direction}
- Volume Surge: {signal.volume_surge}
- Notes: {signal.notes}

Explain in simple terms:
1. Why this trade makes sense
2. What we expect to happen
3. The risk/reward profile
4. When we'll exit

Keep it conversational and educational. Maximum 150 words.
"""

            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a helpful options trading mentor who explains trades clearly to beginners."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=200,
                temperature=0.7
            )
            
            explanation = response.choices[0].message.content.strip()
            self.logger.info("Generated AI trade explanation")
            return explanation
            
        except Exception as e:
            self.logger.error(f"Error generating AI explanation: {e}")
            return self._generate_basic_explanation(signal, contract, position_size, total_cost)
    
    def _generate_basic_explanation(self, signal: SqueezeSignal, contract: OptionContract,
                                  position_size: int, total_cost: float) -> str:
        """Generate basic explanation without AI."""
        
        direction = "bullish" if signal.signal_type == "bullish_breakout" else "bearish"
        option_type = "call" if contract.option_type == "call" else "put"
        
        explanation = f"""
🎯 TRADE EXPLANATION:

We're buying {position_size} {contract.option_type.upper()} option(s) on {signal.symbol} because:

📈 SIGNAL: {signal.signal_type.replace('_', ' ').title()}
The TTM Squeeze indicator shows {signal.symbol} is breaking out in a {direction} direction with {signal.strength:.0%} confidence.

📊 TECHNICAL SETUP:
• Current Price: ${signal.current_price:.2f}
• Strike Price: ${contract.strike:.2f}
• RSI: {signal.rsi:.1f} (momentum indicator)
• Volume: {"Above average" if signal.volume_surge else "Normal"}

💰 POSITION:
• Cost: ${total_cost:.2f} for {position_size} contract(s)
• Max Loss: 50% (${total_cost * 0.5:.2f})
• Target Profit: 100% (${total_cost:.2f})

🎯 STRATEGY:
We expect {signal.symbol} to move {"higher" if option_type == "call" else "lower"}, making our {option_type} options more valuable. We'll exit at 50% loss or 100% gain.
"""
        
        return explanation.strip()
    
    def explain_exit(self, symbol: str, reason: str, pnl: float) -> str:
        """Explain why a position was closed."""
        
        if self.client:
            return self._generate_ai_exit_explanation(symbol, reason, pnl)
        else:
            return self._generate_basic_exit_explanation(symbol, reason, pnl)
    
    def _generate_ai_exit_explanation(self, symbol: str, reason: str, pnl: float) -> str:
        """Generate AI explanation for position exit."""
        try:
            prompt = f"""
Explain why we closed our {symbol} options position:
- Reason: {reason}
- P&L: ${pnl:.2f}

Keep it brief and educational for beginners. Maximum 50 words.
"""
            
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a trading mentor explaining position exits."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=75,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            self.logger.error(f"Error generating AI exit explanation: {e}")
            return self._generate_basic_exit_explanation(symbol, reason, pnl)
    
    def _generate_basic_exit_explanation(self, symbol: str, reason: str, pnl: float) -> str:
        """Generate basic exit explanation."""
        
        if reason == "profit_taken":
            return f"✅ {symbol} position closed at target profit: ${pnl:.2f} gain! Our strategy worked as planned."
        elif reason == "stopped_out":
            return f"🛑 {symbol} position stopped out: ${pnl:.2f} loss. Risk management protected us from larger losses."
        else:
            return f"📊 {symbol} position closed ({reason}): ${pnl:.2f} P&L."
    
    def get_market_insight(self, symbols: list, signals_found: int) -> str:
        """Generate market insight based on scanning results."""
        
        if self.client:
            return self._generate_ai_market_insight(symbols, signals_found)
        else:
            return self._generate_basic_market_insight(symbols, signals_found)
    
    def _generate_ai_market_insight(self, symbols: list, signals_found: int) -> str:
        """Generate AI market insight."""
        try:
            prompt = f"""
We just scanned {len(symbols)} stocks and found {signals_found} trading signals.
Provide a brief market insight for options traders. What does this suggest about current market conditions?
Maximum 75 words.
"""
            
            response = self.client.chat.completions.create(
                model="gpt-3.5-turbo",
                messages=[
                    {"role": "system", "content": "You are a market analyst providing insights to options traders."},
                    {"role": "user", "content": prompt}
                ],
                max_tokens=100,
                temperature=0.7
            )
            
            return response.choices[0].message.content.strip()
            
        except Exception as e:
            self.logger.error(f"Error generating market insight: {e}")
            return self._generate_basic_market_insight(symbols, signals_found)
    
    def _generate_basic_market_insight(self, symbols: list, signals_found: int) -> str:
        """Generate basic market insight."""
        
        signal_rate = signals_found / len(symbols) if symbols else 0
        
        if signal_rate > 0.1:  # More than 10% of stocks have signals
            return f"🔥 Active market: {signals_found}/{len(symbols)} stocks showing signals. Good opportunities available!"
        elif signal_rate > 0.05:  # 5-10% have signals
            return f"📊 Moderate activity: {signals_found}/{len(symbols)} signals found. Selective opportunities."
        else:  # Less than 5% have signals
            return f"😴 Quiet market: Only {signals_found}/{len(symbols)} signals. Being patient for quality setups."
