#!/usr/bin/env python3
"""
Test script for AutoPilot Options Trader
Verifies all components work correctly
"""

import sys
import asyncio
import logging
from datetime import datetime
from pathlib import Path

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

def test_imports():
    """Test all required imports."""
    print("🧪 Testing imports...")
    
    try:
        # Core dependencies
        import pandas as pd
        import numpy as np
        import PySimpleGUI as sg
        import yfinance as yf
        import requests
        print("✅ Core dependencies imported")
        
        # Project modules
        from config import Config
        from utils.logger import setup_logging
        from data.stock_universe import StockUniverse
        from data.options_fetcher import OptionsDataFetcher
        from strategies.ttm_squeeze import TTMSqueezeScreener
        from trade_executor import TradeExecutor
        print("✅ Project modules imported")
        
        return True
        
    except ImportError as e:
        print(f"❌ Import error: {e}")
        return False

def test_configuration():
    """Test configuration loading."""
    print("🧪 Testing configuration...")
    
    try:
        from config import Config
        config = Config()
        
        # Test basic properties
        assert hasattr(config, 'paper_trading')
        assert hasattr(config, 'max_trades_per_day')
        assert config.get('trading.paper_trading') is not None
        
        print("✅ Configuration loaded successfully")
        print(f"   Paper trading: {config.paper_trading}")
        print(f"   Max trades/day: {config.max_trades_per_day}")
        
        return True
        
    except Exception as e:
        print(f"❌ Configuration error: {e}")
        return False

def test_logging():
    """Test logging setup."""
    print("🧪 Testing logging...")
    
    try:
        from utils.logger import setup_logging, get_trade_logger
        
        # Setup logging
        setup_logging()
        logger = logging.getLogger("test")
        
        # Test basic logging
        logger.info("Test log message")
        
        # Test trade logger
        trade_logger = get_trade_logger()
        
        print("✅ Logging system working")
        return True
        
    except Exception as e:
        print(f"❌ Logging error: {e}")
        return False

async def test_stock_universe():
    """Test stock universe loading."""
    print("🧪 Testing stock universe...")
    
    try:
        from config import Config
        from data.stock_universe import StockUniverse
        
        config = Config()
        universe = StockUniverse(config)
        
        # Test loading symbols (use fallback list)
        symbols = await universe.load_sp500_symbols()
        
        if len(symbols) > 0:
            print(f"✅ Loaded {len(symbols)} symbols")
            print(f"   Sample symbols: {symbols[:5]}")
            return True
        else:
            print("❌ No symbols loaded")
            return False
            
    except Exception as e:
        print(f"❌ Stock universe error: {e}")
        return False

async def test_options_fetcher():
    """Test options data fetcher."""
    print("🧪 Testing options fetcher...")
    
    try:
        from config import Config
        from data.options_fetcher import OptionsDataFetcher
        
        config = Config()
        fetcher = OptionsDataFetcher(config)
        
        # Test with a simple symbol (this will use simulation)
        chain = await fetcher.get_option_chain("AAPL")
        
        if chain and ('calls' in chain or 'puts' in chain):
            calls = len(chain.get('calls', []))
            puts = len(chain.get('puts', []))
            print(f"✅ Options fetcher working")
            print(f"   AAPL options: {calls} calls, {puts} puts")
            return True
        else:
            print("❌ No option data returned")
            return False
            
    except Exception as e:
        print(f"❌ Options fetcher error: {e}")
        return False

async def test_ttm_screener():
    """Test TTM Squeeze screener."""
    print("🧪 Testing TTM screener...")
    
    try:
        from config import Config
        from strategies.ttm_squeeze import TTMSqueezeScreener
        
        config = Config()
        screener = TTMSqueezeScreener(config)
        
        # Test with a few symbols
        test_symbols = ['AAPL', 'MSFT', 'GOOGL']
        signals = await screener.scan_symbols(test_symbols)
        
        print(f"✅ TTM screener working")
        print(f"   Scanned {len(test_symbols)} symbols, found {len(signals)} signals")
        
        if signals:
            signal = signals[0]
            print(f"   Sample signal: {signal.symbol} - {signal.signal_type} (strength: {signal.strength:.2f})")
        
        return True
        
    except Exception as e:
        print(f"❌ TTM screener error: {e}")
        return False

def test_trade_executor():
    """Test trade executor."""
    print("🧪 Testing trade executor...")
    
    try:
        from config import Config
        from trade_executor import TradeExecutor
        
        config = Config()
        executor = TradeExecutor(config)
        
        # Test basic functionality
        positions = executor.get_open_positions()
        daily_pnl = executor.get_daily_pnl()
        
        print("✅ Trade executor working")
        print(f"   Open positions: {len(positions)}")
        print(f"   Daily P&L: ${daily_pnl:.2f}")
        
        return True
        
    except Exception as e:
        print(f"❌ Trade executor error: {e}")
        return False

def test_gui_components():
    """Test GUI components (without actually showing GUI)."""
    print("🧪 Testing GUI components...")
    
    try:
        import PySimpleGUI as sg
        from config import Config
        from gui import TradingGUI
        
        config = Config()
        
        # Test GUI creation (don't run it)
        gui = TradingGUI(config)
        layout = gui.create_layout()
        
        print("✅ GUI components working")
        print(f"   Layout created with {len(layout)} sections")
        
        return True
        
    except Exception as e:
        print(f"❌ GUI error: {e}")
        return False

async def run_all_tests():
    """Run all tests."""
    print("🚀 AutoPilot Options Trader - System Test")
    print("=" * 50)
    
    tests = [
        ("Imports", test_imports),
        ("Configuration", test_configuration),
        ("Logging", test_logging),
        ("Stock Universe", test_stock_universe),
        ("Options Fetcher", test_options_fetcher),
        ("TTM Screener", test_ttm_screener),
        ("Trade Executor", test_trade_executor),
        ("GUI Components", test_gui_components),
    ]
    
    passed = 0
    failed = 0
    
    for test_name, test_func in tests:
        print(f"\n--- {test_name} ---")
        try:
            if asyncio.iscoroutinefunction(test_func):
                result = await test_func()
            else:
                result = test_func()
            
            if result:
                passed += 1
            else:
                failed += 1
                
        except Exception as e:
            print(f"❌ {test_name} failed with exception: {e}")
            failed += 1
    
    print("\n" + "=" * 50)
    print(f"📊 Test Results: {passed} passed, {failed} failed")
    
    if failed == 0:
        print("🎉 All tests passed! System is ready to use.")
        print("\n📋 Next steps:")
        print("1. Set up your .env file with Alpaca API keys")
        print("2. Run: python main.py")
        print("3. Start with paper trading")
    else:
        print("❌ Some tests failed. Please check the errors above.")
        print("💡 Common issues:")
        print("   - Missing dependencies (run: pip install -r requirements.txt)")
        print("   - Missing API keys in .env file")
        print("   - Network connectivity issues")
    
    return failed == 0

def main():
    """Main test function."""
    try:
        # Run async tests
        result = asyncio.run(run_all_tests())
        return result
    except KeyboardInterrupt:
        print("\n⏹️  Tests interrupted by user")
        return False
    except Exception as e:
        print(f"\n💥 Unexpected error: {e}")
        return False

if __name__ == "__main__":
    success = main()
    sys.exit(0 if success else 1)
