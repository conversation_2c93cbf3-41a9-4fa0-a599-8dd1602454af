2025-06-10 07:04:59 - test - INFO - Test log message
2025-06-10 07:04:59 - trades - INFO - timestamp,symbol,action,option_type,strike,expiry,quantity,premium,total_cost,stop_loss,take_profit,signal_strength,notes
2025-06-10 07:04:59 - config - INFO - Loaded configuration from config.json
2025-06-10 07:04:59 - data.stock_universe - ERROR - Error loading S&P 500 symbols: Missing optional dependency 'lxml'.  Use pip or conda to install lxml.
2025-06-10 07:04:59 - data.stock_universe - WARNING - Using fallback symbols: 24 symbols
2025-06-10 07:04:59 - config - INFO - Loaded configuration from config.json
2025-06-10 07:04:59 - config - INFO - Loaded configuration from config.json
2025-06-10 07:04:59 - strategies.ttm_squeeze - INFO - Scanning 3 symbols for TTM Squeeze signals...
2025-06-10 07:05:03 - strategies.ttm_squeeze - INFO - Found 0 TTM Squeeze signals
2025-06-10 07:05:03 - config - INFO - Loaded configuration from config.json
2025-06-10 07:06:13 - config - INFO - Loaded configuration from config.json
2025-06-10 07:06:13 - data.stock_universe - ERROR - Error loading S&P 500 symbols: Missing optional dependency 'lxml'.  Use pip or conda to install lxml.
2025-06-10 07:06:13 - data.stock_universe - WARNING - Using fallback symbols: 24 symbols
2025-06-10 07:06:13 - strategies.ttm_squeeze - INFO - Scanning 10 symbols for TTM Squeeze signals...
2025-06-10 07:06:16 - strategies.ttm_squeeze - INFO - Found 0 TTM Squeeze signals
2025-06-10 07:11:18 - config - INFO - Loaded configuration from config.json
2025-06-10 07:11:18 - data.stock_universe - ERROR - Error loading S&P 500 symbols: Missing optional dependency 'lxml'.  Use pip or conda to install lxml.
2025-06-10 07:11:18 - data.stock_universe - WARNING - Using fallback symbols: 24 symbols
2025-06-10 07:11:18 - strategies.ttm_squeeze - INFO - Scanning 10 symbols for TTM Squeeze signals...
2025-06-10 07:11:20 - strategies.ttm_squeeze - INFO - Found 0 TTM Squeeze signals
2025-06-10 07:17:43 - config - INFO - Loaded configuration from config.json
2025-06-10 07:18:44 - config - INFO - Loaded configuration from config.json
2025-06-10 07:18:44 - data.options_fetcher - INFO - No API keys - running in demo mode
2025-06-10 07:18:44 - trade_executor - INFO - No API keys - running in demo mode
2025-06-10 07:18:44 - data.stock_universe - ERROR - Error loading S&P 500 symbols: Missing optional dependency 'lxml'.  Use pip or conda to install lxml.
2025-06-10 07:18:44 - data.stock_universe - WARNING - Using fallback symbols: 24 symbols
2025-06-10 07:18:44 - strategies.ttm_squeeze - INFO - Scanning 15 symbols for TTM Squeeze signals...
2025-06-10 07:18:48 - strategies.ttm_squeeze - INFO - Found 0 TTM Squeeze signals
2025-06-10 07:18:48 - trades - INFO - 2025-06-10T07:18:48.027313,AAPL,buy,call,200,2025-06-20,1,1.455,145.5,0.73,2.91,0.85,"TTM Squeeze bullish_breakout - TTM Squeeze ending with strong bullish momentum; Volume surge detected"
2025-06-10 07:18:48 - trade_executor - INFO - Executed trade: buy 1 AAPL250620C00200000
2025-06-10 07:26:02 - config - INFO - Loaded configuration from config.json
2025-06-10 07:26:02 - data.options_fetcher - INFO - No API keys - running in demo mode
2025-06-10 07:26:02 - trade_executor - INFO - No API keys - running in demo mode
2025-06-10 07:26:03 - data.stock_universe - INFO - Loaded 500 S&P 500 symbols from FMP API
2025-06-10 07:26:03 - strategies.ttm_squeeze - INFO - Scanning 15 symbols for TTM Squeeze signals...
2025-06-10 07:26:07 - strategies.ttm_squeeze - INFO - Found 0 TTM Squeeze signals
2025-06-10 07:26:07 - trades - INFO - 2025-06-10T07:26:07.909633,AAPL,buy,call,200,2025-06-20,1,1.455,145.5,0.73,2.91,0.85,"TTM Squeeze bullish_breakout - TTM Squeeze ending with strong bullish momentum; Volume surge detected"
2025-06-10 07:26:07 - trade_executor - INFO - Executed trade: buy 1 AAPL250620C00200000
2025-06-10 07:28:37 - config - INFO - Loaded configuration from config.json
2025-06-10 07:28:37 - data.options_fetcher - INFO - No API keys - running in demo mode
2025-06-10 07:28:37 - trade_executor - INFO - No API keys - running in demo mode
2025-06-10 07:28:37 - ai_explainer - INFO - OpenAI API initialized for trade explanations
2025-06-10 07:28:38 - data.stock_universe - INFO - Loaded 500 S&P 500 symbols from FMP API
2025-06-10 07:28:38 - strategies.ttm_squeeze - INFO - Scanning 25 symbols for TTM Squeeze signals...
2025-06-10 07:28:43 - strategies.ttm_squeeze - INFO - Found 0 TTM Squeeze signals
2025-06-10 07:28:48 - httpx - INFO - HTTP Request: POST https://api.openai.com/v1/chat/completions "HTTP/1.1 200 OK"
2025-06-10 07:41:48 - config - INFO - Loaded configuration from config.json
2025-06-10 07:41:48 - ml_engine - INFO - Models loaded successfully
2025-06-10 07:41:48 - ml_engine - INFO - Machine Learning Engine initialized
2025-06-10 07:41:48 - advanced_options_system - INFO - Advanced Options Trading System initialized
2025-06-10 07:41:48 - advanced_options_system - INFO - Analyzing 5 symbols for trading opportunities
2025-06-10 07:41:49 - strategy_selector - INFO - Ranked 4 strategies for AAPL
2025-06-10 07:41:49 - risk_manager - INFO - Approved 0/4 strategies for AAPL
2025-06-10 07:41:50 - strategy_selector - INFO - Ranked 4 strategies for TSLA
2025-06-10 07:41:50 - risk_manager - INFO - Approved 0/4 strategies for TSLA
2025-06-10 07:41:52 - strategy_selector - INFO - Ranked 4 strategies for SPY
2025-06-10 07:41:52 - risk_manager - INFO - Approved 0/4 strategies for SPY
2025-06-10 07:41:52 - strategy_selector - INFO - Ranked 4 strategies for NVDA
2025-06-10 07:41:52 - risk_manager - INFO - Approved 0/4 strategies for NVDA
2025-06-10 07:41:54 - strategy_selector - INFO - Ranked 4 strategies for XLE
2025-06-10 07:41:54 - risk_manager - INFO - Approved 0/4 strategies for XLE
