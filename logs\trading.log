2025-06-10 07:04:59 - test - INFO - Test log message
2025-06-10 07:04:59 - trades - INFO - timestamp,symbol,action,option_type,strike,expiry,quantity,premium,total_cost,stop_loss,take_profit,signal_strength,notes
2025-06-10 07:04:59 - config - INFO - Loaded configuration from config.json
2025-06-10 07:04:59 - data.stock_universe - ERROR - Error loading S&P 500 symbols: Missing optional dependency 'lxml'.  Use pip or conda to install lxml.
2025-06-10 07:04:59 - data.stock_universe - WARNING - Using fallback symbols: 24 symbols
2025-06-10 07:04:59 - config - INFO - Loaded configuration from config.json
2025-06-10 07:04:59 - config - INFO - Loaded configuration from config.json
2025-06-10 07:04:59 - strategies.ttm_squeeze - INFO - Scanning 3 symbols for TTM Squeeze signals...
2025-06-10 07:05:03 - strategies.ttm_squeeze - INFO - Found 0 TTM Squeeze signals
2025-06-10 07:05:03 - config - INFO - Loaded configuration from config.json
2025-06-10 07:06:13 - config - INFO - Loaded configuration from config.json
2025-06-10 07:06:13 - data.stock_universe - ERROR - Error loading S&P 500 symbols: Missing optional dependency 'lxml'.  Use pip or conda to install lxml.
2025-06-10 07:06:13 - data.stock_universe - WARNING - Using fallback symbols: 24 symbols
2025-06-10 07:06:13 - strategies.ttm_squeeze - INFO - Scanning 10 symbols for TTM Squeeze signals...
2025-06-10 07:06:16 - strategies.ttm_squeeze - INFO - Found 0 TTM Squeeze signals
2025-06-10 07:11:18 - config - INFO - Loaded configuration from config.json
2025-06-10 07:11:18 - data.stock_universe - ERROR - Error loading S&P 500 symbols: Missing optional dependency 'lxml'.  Use pip or conda to install lxml.
2025-06-10 07:11:18 - data.stock_universe - WARNING - Using fallback symbols: 24 symbols
2025-06-10 07:11:18 - strategies.ttm_squeeze - INFO - Scanning 10 symbols for TTM Squeeze signals...
2025-06-10 07:11:20 - strategies.ttm_squeeze - INFO - Found 0 TTM Squeeze signals
