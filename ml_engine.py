"""
Machine Learning Engine for Options Trading
Learns from trade outcomes to improve strategy selection and execution
"""

import numpy as np
import pandas as pd
from typing import Dict, List, Optional, Tuple, Any
import logging
from datetime import datetime, timedelta
import pickle
import json
from pathlib import Path
from sklearn.ensemble import RandomForestRegressor, GradientBoostingClassifier
from sklearn.preprocessing import StandardScaler
from sklearn.model_selection import train_test_split
from sklearn.metrics import accuracy_score, mean_squared_error

from trading_types import (
    StrategyType, StrategyScore, Position, MarketConditions, MarketRegime
)

class MachineLearningEngine:
    """ML engine for continuous learning and strategy optimization."""
    
    def __init__(self, config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Model storage
        self.models_dir = Path("models")
        self.models_dir.mkdir(exist_ok=True)
        
        # Feature engineering
        self.feature_scaler = StandardScaler()
        
        # Models
        self.strategy_selector_model = None
        self.return_predictor_model = None
        self.risk_predictor_model = None
        
        # Training data
        self.trade_history = []
        self.market_features = []
        
        # Performance tracking
        self.strategy_performance = {}
        self.model_performance = {}
        
        # Load existing models
        self._load_models()
        
        self.logger.info("Machine Learning Engine initialized")
    
    async def record_trade(self, symbol: str, market_conditions: MarketConditions,
                         strategy_score: StrategyScore, position: Position):
        """Record a new trade for learning."""
        
        trade_record = {
            'timestamp': datetime.now(),
            'symbol': symbol,
            'strategy': strategy_score.strategy.value,
            'market_regime': market_conditions.regime.value,
            'iv_rank': market_conditions.volatility_profile.iv_rank,
            'price_trend': market_conditions.price_trend,
            'days_to_earnings': market_conditions.days_to_earnings,
            'volume_ratio': market_conditions.volume_profile.get('volume_ratio', 1.0),
            'strategy_score': strategy_score.score,
            'expected_return': strategy_score.expected_return,
            'max_risk': strategy_score.max_risk,
            'win_probability': strategy_score.win_probability,
            'position_cost': position.total_cost,
            'max_profit': position.max_profit,
            'max_loss': position.max_loss,
            'days_to_expiry': position.days_to_expiry,
            'outcome': None,  # Will be filled when position closes
            'actual_return': None,
            'actual_risk': None,
            'hold_time': None
        }
        
        self.trade_history.append(trade_record)
        self.logger.info(f"Recorded trade: {symbol} {strategy_score.strategy.value}")
    
    async def record_trade_outcome(self, position: Position, pnl: float, exit_reason: str):
        """Record the outcome of a completed trade."""
        
        # Find the corresponding trade record
        for trade in reversed(self.trade_history):
            if (trade['symbol'] == position.symbol and 
                trade['strategy'] == position.strategy.value and
                trade['outcome'] is None):
                
                # Calculate metrics
                actual_return = pnl / abs(position.total_cost) if position.total_cost != 0 else 0
                actual_risk = min(0, pnl / abs(position.total_cost)) if position.total_cost != 0 else 0
                hold_time = (datetime.now() - position.entry_time).days
                
                # Update trade record
                trade.update({
                    'outcome': exit_reason,
                    'actual_return': actual_return,
                    'actual_risk': actual_risk,
                    'hold_time': hold_time,
                    'pnl': pnl
                })
                
                # Update strategy performance
                self._update_strategy_performance(position.strategy, actual_return, exit_reason)
                
                # Trigger model retraining if enough new data
                if len([t for t in self.trade_history if t['outcome'] is not None]) % 50 == 0:
                    await self._retrain_models()
                
                self.logger.info(f"Recorded outcome: {position.symbol} {exit_reason} P&L: ${pnl:.2f}")
                break
    
    async def predict_strategy_performance(self, symbol: str, market_conditions: MarketConditions,
                                         strategy: StrategyType) -> Dict[str, float]:
        """Predict strategy performance using ML models."""
        
        if not self.strategy_selector_model:
            # Return default predictions if no model trained yet
            return {
                'predicted_return': 0.1,
                'predicted_risk': 0.5,
                'win_probability': 0.6,
                'confidence': 0.5
            }
        
        # Extract features
        features = self._extract_features(symbol, market_conditions, strategy)
        features_scaled = self.feature_scaler.transform([features])
        
        # Make predictions
        try:
            predicted_return = self.return_predictor_model.predict(features_scaled)[0]
            predicted_risk = self.risk_predictor_model.predict(features_scaled)[0]
            win_probability = self.strategy_selector_model.predict_proba(features_scaled)[0][1]
            
            # Calculate confidence based on model certainty
            confidence = self._calculate_prediction_confidence(features_scaled)
            
            return {
                'predicted_return': predicted_return,
                'predicted_risk': abs(predicted_risk),
                'win_probability': win_probability,
                'confidence': confidence
            }
            
        except Exception as e:
            self.logger.error(f"Error making ML predictions: {e}")
            return {
                'predicted_return': 0.1,
                'predicted_risk': 0.5,
                'win_probability': 0.6,
                'confidence': 0.5
            }
    
    async def get_strategy_recommendations(self, symbol: str, 
                                        market_conditions: MarketConditions) -> List[Tuple[StrategyType, float]]:
        """Get ML-based strategy recommendations."""
        
        recommendations = []
        
        # Test all strategies
        for strategy in StrategyType:
            predictions = await self.predict_strategy_performance(symbol, market_conditions, strategy)
            
            # Calculate composite score
            score = (
                predictions['predicted_return'] * 0.4 +
                predictions['win_probability'] * 0.3 +
                (1 - predictions['predicted_risk']) * 0.2 +
                predictions['confidence'] * 0.1
            )
            
            recommendations.append((strategy, score))
        
        # Sort by score
        recommendations.sort(key=lambda x: x[1], reverse=True)
        
        return recommendations[:5]  # Top 5 recommendations
    
    def _extract_features(self, symbol: str, market_conditions: MarketConditions, 
                         strategy: StrategyType) -> List[float]:
        """Extract features for ML models."""
        
        features = [
            # Market regime (one-hot encoded)
            1.0 if market_conditions.regime == MarketRegime.TRENDING_UP else 0.0,
            1.0 if market_conditions.regime == MarketRegime.TRENDING_DOWN else 0.0,
            1.0 if market_conditions.regime == MarketRegime.RANGING else 0.0,
            1.0 if market_conditions.regime == MarketRegime.HIGH_VOLATILITY else 0.0,
            1.0 if market_conditions.regime == MarketRegime.LOW_VOLATILITY else 0.0,
            
            # Volatility features
            market_conditions.volatility_profile.iv_rank / 100.0,
            market_conditions.volatility_profile.implied_vol,
            market_conditions.volatility_profile.historical_vol,
            market_conditions.volatility_profile.vol_skew,
            
            # Price and trend features
            market_conditions.price_trend,
            
            # Time features
            market_conditions.days_to_earnings or 999,  # Large number if no earnings
            
            # Volume features
            market_conditions.volume_profile.get('volume_ratio', 1.0),
            market_conditions.volume_profile.get('volume_trend', 1.0),
            
            # Strategy features (one-hot encoded for major categories)
            1.0 if strategy in [StrategyType.LONG_CALL, StrategyType.LONG_PUT] else 0.0,
            1.0 if strategy in [StrategyType.IRON_CONDOR, StrategyType.IRON_BUTTERFLY] else 0.0,
            1.0 if strategy in [StrategyType.LONG_STRADDLE, StrategyType.LONG_STRANGLE] else 0.0,
            1.0 if strategy in [StrategyType.COVERED_CALL, StrategyType.CASH_SECURED_PUT] else 0.0,
        ]
        
        return features
    
    async def _retrain_models(self):
        """Retrain ML models with new data."""
        
        completed_trades = [t for t in self.trade_history if t['outcome'] is not None]
        
        if len(completed_trades) < 20:  # Need minimum data
            self.logger.info("Insufficient data for model retraining")
            return
        
        self.logger.info(f"Retraining models with {len(completed_trades)} completed trades")
        
        try:
            # Prepare training data
            X, y_return, y_risk, y_success = self._prepare_training_data(completed_trades)
            
            if len(X) == 0:
                return
            
            # Split data
            X_train, X_test, y_return_train, y_return_test = train_test_split(
                X, y_return, test_size=0.2, random_state=42
            )
            
            # Scale features
            X_train_scaled = self.feature_scaler.fit_transform(X_train)
            X_test_scaled = self.feature_scaler.transform(X_test)
            
            # Train return predictor
            self.return_predictor_model = RandomForestRegressor(
                n_estimators=100, random_state=42, max_depth=10
            )
            self.return_predictor_model.fit(X_train_scaled, y_return_train)
            
            # Train risk predictor
            self.risk_predictor_model = RandomForestRegressor(
                n_estimators=100, random_state=42, max_depth=10
            )
            self.risk_predictor_model.fit(X_train_scaled, y_risk)
            
            # Train strategy selector (binary classification: profitable vs not)
            y_success_binary = [1 if ret > 0 else 0 for ret in y_return]
            self.strategy_selector_model = GradientBoostingClassifier(
                n_estimators=100, random_state=42, max_depth=6
            )
            self.strategy_selector_model.fit(X_train_scaled, y_success_binary)
            
            # Evaluate models
            return_pred = self.return_predictor_model.predict(X_test_scaled)
            return_mse = mean_squared_error(y_return_test, return_pred)
            
            success_pred = self.strategy_selector_model.predict(X_test_scaled)
            success_acc = accuracy_score([1 if ret > 0 else 0 for ret in y_return_test], success_pred)
            
            self.model_performance = {
                'return_mse': return_mse,
                'success_accuracy': success_acc,
                'training_samples': len(X_train),
                'last_trained': datetime.now()
            }
            
            # Save models
            self._save_models()
            
            self.logger.info(f"Models retrained - Return MSE: {return_mse:.4f}, Success Acc: {success_acc:.4f}")
            
        except Exception as e:
            self.logger.error(f"Error retraining models: {e}")
    
    def _prepare_training_data(self, completed_trades: List[Dict]) -> Tuple[List, List, List, List]:
        """Prepare training data from completed trades."""
        
        X = []
        y_return = []
        y_risk = []
        y_success = []
        
        for trade in completed_trades:
            try:
                # Reconstruct market conditions (simplified)
                market_conditions = self._reconstruct_market_conditions(trade)
                strategy = StrategyType(trade['strategy'])
                
                # Extract features
                features = self._extract_features(trade['symbol'], market_conditions, strategy)
                
                X.append(features)
                y_return.append(trade['actual_return'])
                y_risk.append(trade['actual_risk'])
                y_success.append(1 if trade['actual_return'] > 0 else 0)
                
            except Exception as e:
                self.logger.debug(f"Error processing trade for training: {e}")
                continue
        
        return X, y_return, y_risk, y_success
    
    def _reconstruct_market_conditions(self, trade: Dict) -> MarketConditions:
        """Reconstruct market conditions from trade record."""
        # Simplified reconstruction - in production would store full conditions
        from advanced_options_system import VolatilityProfile
        
        vol_profile = VolatilityProfile(
            implied_vol=0.25,  # Default
            historical_vol=0.20,
            iv_rank=trade['iv_rank'],
            iv_percentile=trade['iv_rank'],
            vol_skew=0.0,
            term_structure={}
        )
        
        return MarketConditions(
            regime=MarketRegime(trade['market_regime']),
            volatility_profile=vol_profile,
            price_trend=trade['price_trend'],
            support_resistance=(0, 0),
            earnings_date=None,
            days_to_earnings=trade['days_to_earnings'],
            volume_profile={'volume_ratio': trade['volume_ratio']}
        )
    
    def _calculate_prediction_confidence(self, features_scaled: np.ndarray) -> float:
        """Calculate confidence in ML predictions."""
        
        if not self.strategy_selector_model:
            return 0.5
        
        # Use prediction probability spread as confidence measure
        try:
            probabilities = self.strategy_selector_model.predict_proba(features_scaled)[0]
            confidence = abs(probabilities[1] - probabilities[0])  # Spread between classes
            return min(0.95, max(0.1, confidence))
        except:
            return 0.5
    
    def _update_strategy_performance(self, strategy: StrategyType, actual_return: float, exit_reason: str):
        """Update strategy performance tracking."""
        
        if strategy not in self.strategy_performance:
            self.strategy_performance[strategy] = {
                'total_trades': 0,
                'winning_trades': 0,
                'total_return': 0.0,
                'avg_return': 0.0,
                'win_rate': 0.0,
                'best_return': 0.0,
                'worst_return': 0.0
            }
        
        perf = self.strategy_performance[strategy]
        perf['total_trades'] += 1
        perf['total_return'] += actual_return
        
        if actual_return > 0:
            perf['winning_trades'] += 1
        
        perf['avg_return'] = perf['total_return'] / perf['total_trades']
        perf['win_rate'] = perf['winning_trades'] / perf['total_trades']
        perf['best_return'] = max(perf['best_return'], actual_return)
        perf['worst_return'] = min(perf['worst_return'], actual_return)
    
    def _save_models(self):
        """Save trained models to disk."""
        try:
            if self.return_predictor_model:
                with open(self.models_dir / "return_predictor.pkl", "wb") as f:
                    pickle.dump(self.return_predictor_model, f)
            
            if self.risk_predictor_model:
                with open(self.models_dir / "risk_predictor.pkl", "wb") as f:
                    pickle.dump(self.risk_predictor_model, f)
            
            if self.strategy_selector_model:
                with open(self.models_dir / "strategy_selector.pkl", "wb") as f:
                    pickle.dump(self.strategy_selector_model, f)
            
            # Save scaler
            with open(self.models_dir / "feature_scaler.pkl", "wb") as f:
                pickle.dump(self.feature_scaler, f)
            
            # Save performance data
            with open(self.models_dir / "strategy_performance.json", "w") as f:
                # Convert StrategyType keys to strings for JSON serialization
                perf_data = {k.value: v for k, v in self.strategy_performance.items()}
                json.dump(perf_data, f, indent=2)
            
            self.logger.info("Models saved successfully")
            
        except Exception as e:
            self.logger.error(f"Error saving models: {e}")
    
    def _load_models(self):
        """Load trained models from disk."""
        try:
            # Load models
            if (self.models_dir / "return_predictor.pkl").exists():
                with open(self.models_dir / "return_predictor.pkl", "rb") as f:
                    self.return_predictor_model = pickle.load(f)
            
            if (self.models_dir / "risk_predictor.pkl").exists():
                with open(self.models_dir / "risk_predictor.pkl", "rb") as f:
                    self.risk_predictor_model = pickle.load(f)
            
            if (self.models_dir / "strategy_selector.pkl").exists():
                with open(self.models_dir / "strategy_selector.pkl", "rb") as f:
                    self.strategy_selector_model = pickle.load(f)
            
            # Load scaler
            if (self.models_dir / "feature_scaler.pkl").exists():
                with open(self.models_dir / "feature_scaler.pkl", "rb") as f:
                    self.feature_scaler = pickle.load(f)
            
            # Load performance data
            if (self.models_dir / "strategy_performance.json").exists():
                with open(self.models_dir / "strategy_performance.json", "r") as f:
                    perf_data = json.load(f)
                    # Convert string keys back to StrategyType
                    self.strategy_performance = {
                        StrategyType(k): v for k, v in perf_data.items()
                    }
            
            self.logger.info("Models loaded successfully")
            
        except Exception as e:
            self.logger.warning(f"Could not load existing models: {e}")
    
    def get_performance_summary(self) -> Dict[str, Any]:
        """Get performance summary for all strategies."""
        
        summary = {
            'total_trades': len([t for t in self.trade_history if t['outcome'] is not None]),
            'model_performance': self.model_performance,
            'strategy_performance': {}
        }
        
        for strategy, perf in self.strategy_performance.items():
            summary['strategy_performance'][strategy.value] = perf
        
        return summary
