#!/usr/bin/env python3
"""
Advanced Multi-Strategy Options Trading System
Expert-level algorithmic trader with dynamic strategy selection
"""

import sys
import asyncio
import logging
import numpy as np
import pandas as pd
from datetime import datetime, timedelta
from pathlib import Path
from typing import Dict, List, Optional, Tuple, Any
from dataclasses import dataclass, field
from enum import Enum
import json

# Add project root to path
sys.path.insert(0, str(Path(__file__).parent))

from config import Config
from utils.logger import setup_logging
from trading_types import *
from market_analyzer import MarketAnalyzer
from strategy_selector import StrategySelector
from risk_manager import AdvancedRiskManager
from execution_engine import MultiLegExecutor
from ml_engine import MachineLearningEngine

# Types are now imported from trading_types.py

class AdvancedOptionsSystem:
    """Advanced multi-strategy options trading system."""
    
    def __init__(self, config: Config):
        self.config = config
        self.logger = logging.getLogger(__name__)
        
        # Initialize components
        self.market_analyzer = MarketAnalyzer(config)
        self.strategy_selector = StrategySelector(config)
        self.risk_manager = AdvancedRiskManager(config)
        self.execution_engine = MultiLegExecutor(config)
        self.ml_engine = MachineLearningEngine(config)
        
        # Portfolio state
        self.positions: Dict[str, List[Position]] = {}
        self.portfolio_greeks = Greeks(0, 0, 0, 0, 0)
        self.portfolio_heat = 0.0
        
        # Performance tracking
        self.strategy_performance: Dict[StrategyType, Dict] = {}
        self.trade_history: List[Dict] = []
        
        self.logger.info("Advanced Options Trading System initialized")
    
    async def analyze_and_trade(self, symbols: List[str]) -> Dict[str, Any]:
        """Main trading loop - analyze market and execute strategies."""
        results = {
            'analyzed_symbols': len(symbols),
            'strategies_considered': 0,
            'trades_executed': 0,
            'portfolio_summary': {},
            'market_insights': {}
        }
        
        self.logger.info(f"Analyzing {len(symbols)} symbols for trading opportunities")
        
        for symbol in symbols:
            try:
                # Step 1: Analyze market conditions
                market_conditions = await self.market_analyzer.analyze_symbol(symbol)
                if not market_conditions:
                    continue
                
                # Step 2: Get strategy recommendations
                strategy_scores = await self.strategy_selector.rank_strategies(
                    symbol, market_conditions
                )
                results['strategies_considered'] += len(strategy_scores)
                
                # Step 3: Risk assessment
                approved_strategies = self.risk_manager.filter_strategies(
                    symbol, strategy_scores, self.positions, self.portfolio_heat
                )
                
                # Step 4: Execute best strategy
                if approved_strategies:
                    best_strategy = approved_strategies[0]
                    position = await self.execution_engine.execute_strategy(
                        symbol, best_strategy, market_conditions
                    )
                    
                    if position:
                        self._add_position(symbol, position)
                        results['trades_executed'] += 1
                        
                        # Update ML model with new trade
                        await self.ml_engine.record_trade(
                            symbol, market_conditions, best_strategy, position
                        )
                
                # Store market insights
                results['market_insights'][symbol] = {
                    'regime': market_conditions.regime.value,
                    'iv_rank': market_conditions.volatility_profile.iv_rank,
                    'top_strategy': strategy_scores[0].strategy.value if strategy_scores else None,
                    'top_score': strategy_scores[0].score if strategy_scores else 0
                }
                
            except Exception as e:
                self.logger.error(f"Error analyzing {symbol}: {e}")
                continue
        
        # Update portfolio summary
        await self._update_portfolio_metrics()
        results['portfolio_summary'] = self._get_portfolio_summary()
        
        return results
    
    async def monitor_positions(self) -> Dict[str, Any]:
        """Monitor and manage existing positions."""
        monitoring_results = {
            'positions_monitored': 0,
            'adjustments_made': 0,
            'positions_closed': 0,
            'portfolio_greeks': self.portfolio_greeks.__dict__
        }
        
        for symbol, positions in self.positions.items():
            for position in positions:
                if position.status != 'open':
                    continue
                
                monitoring_results['positions_monitored'] += 1
                
                # Check exit conditions
                exit_signal = await self._check_exit_conditions(position)
                if exit_signal:
                    await self._close_position(position, exit_signal['reason'])
                    monitoring_results['positions_closed'] += 1
                    continue
                
                # Check adjustment opportunities
                adjustment = await self._check_adjustments(position)
                if adjustment:
                    await self._adjust_position(position, adjustment)
                    monitoring_results['adjustments_made'] += 1
        
        return monitoring_results
    
    def _add_position(self, symbol: str, position: Position):
        """Add position to portfolio."""
        if symbol not in self.positions:
            self.positions[symbol] = []
        self.positions[symbol].append(position)
        
        # Update portfolio Greeks
        self._update_portfolio_greeks()
        
        self.logger.info(f"Added {position.strategy.value} position for {symbol}")
    
    async def _update_portfolio_metrics(self):
        """Update portfolio-level metrics."""
        total_value = 0
        total_risk = 0
        
        for symbol, positions in self.positions.items():
            for position in positions:
                if position.status == 'open':
                    total_value += abs(position.total_cost)
                    total_risk += position.max_loss
        
        self.portfolio_heat = total_risk / max(total_value, 1)
        self._update_portfolio_greeks()
    
    def _update_portfolio_greeks(self):
        """Calculate portfolio-level Greeks."""
        total_delta = total_gamma = total_theta = total_vega = total_rho = 0
        
        for symbol, positions in self.positions.items():
            for position in positions:
                if position.status == 'open':
                    total_delta += position.greeks.delta
                    total_gamma += position.greeks.gamma
                    total_theta += position.greeks.theta
                    total_vega += position.greeks.vega
                    total_rho += position.greeks.rho
        
        self.portfolio_greeks = Greeks(
            total_delta, total_gamma, total_theta, total_vega, total_rho
        )
    
    def _get_portfolio_summary(self) -> Dict[str, Any]:
        """Get portfolio summary statistics."""
        open_positions = sum(
            len([p for p in positions if p.status == 'open'])
            for positions in self.positions.values()
        )
        
        total_value = sum(
            sum(p.total_cost for p in positions if p.status == 'open')
            for positions in self.positions.values()
        )
        
        return {
            'open_positions': open_positions,
            'total_value': total_value,
            'portfolio_heat': self.portfolio_heat,
            'portfolio_greeks': self.portfolio_greeks.__dict__,
            'symbols_traded': len(self.positions)
        }
    
    async def _check_exit_conditions(self, position: Position) -> Optional[Dict]:
        """Check if position should be closed."""
        # Profit target hit
        current_pnl = await self._calculate_position_pnl(position)
        if current_pnl >= position.target_profit:
            return {'reason': 'profit_target', 'pnl': current_pnl}
        
        # Stop loss hit
        if current_pnl <= -position.stop_loss:
            return {'reason': 'stop_loss', 'pnl': current_pnl}
        
        # Time decay (theta) considerations
        if position.days_to_expiry <= 7 and position.strategy in [
            StrategyType.LONG_STRADDLE, StrategyType.LONG_STRANGLE
        ]:
            return {'reason': 'time_decay', 'pnl': current_pnl}
        
        # Greeks thresholds
        if abs(position.greeks.delta) > 0.8:  # Too directional
            return {'reason': 'delta_threshold', 'pnl': current_pnl}
        
        return None
    
    async def _calculate_position_pnl(self, position: Position) -> float:
        """Calculate current P&L for position."""
        # Simplified P&L calculation - would use real option pricing in production
        return np.random.uniform(-position.max_loss, position.max_profit)
    
    async def _check_adjustments(self, position: Position) -> Optional[Dict]:
        """Check if position needs adjustment."""
        # Example: Roll options closer to expiry
        if position.days_to_expiry <= 14 and position.strategy == StrategyType.COVERED_CALL:
            return {'type': 'roll_forward', 'reason': 'approaching_expiry'}
        
        return None
    
    async def _adjust_position(self, position: Position, adjustment: Dict):
        """Adjust existing position."""
        self.logger.info(f"Adjusting position {position.symbol}: {adjustment['type']}")
        # Implementation would depend on adjustment type
    
    async def _close_position(self, position: Position, reason: str):
        """Close position."""
        position.status = 'closed'
        pnl = await self._calculate_position_pnl(position)
        
        # Record trade outcome for ML
        await self.ml_engine.record_trade_outcome(position, pnl, reason)
        
        self.logger.info(f"Closed {position.symbol} position: {reason}, P&L: ${pnl:.2f}")

# This is the main class - I'll continue with the component classes in separate files
async def main():
    """Demo the advanced options system."""
    setup_logging()
    config = Config()
    
    system = AdvancedOptionsSystem(config)
    
    # Demo symbols
    symbols = ['AAPL', 'MSFT', 'GOOGL', 'TSLA', 'NVDA']
    
    print("🚀 Advanced Multi-Strategy Options Trading System")
    print("=" * 60)
    
    # Analyze and trade
    results = await system.analyze_and_trade(symbols)
    
    print(f"📊 Analysis Results:")
    print(f"   Symbols analyzed: {results['analyzed_symbols']}")
    print(f"   Strategies considered: {results['strategies_considered']}")
    print(f"   Trades executed: {results['trades_executed']}")
    
    # Monitor positions
    monitoring = await system.monitor_positions()
    print(f"\n📈 Position Monitoring:")
    print(f"   Positions monitored: {monitoring['positions_monitored']}")
    print(f"   Adjustments made: {monitoring['adjustments_made']}")
    print(f"   Positions closed: {monitoring['positions_closed']}")
    
    print(f"\n💼 Portfolio Summary:")
    portfolio = results['portfolio_summary']
    print(f"   Open positions: {portfolio['open_positions']}")
    print(f"   Portfolio value: ${portfolio['total_value']:.2f}")
    print(f"   Portfolio heat: {portfolio['portfolio_heat']:.2%}")

if __name__ == "__main__":
    asyncio.run(main())
